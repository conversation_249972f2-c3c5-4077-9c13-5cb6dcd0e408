import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import https from 'https';
import http from 'http';

(async () => {
  // Launch the browser and open a new blank page
  const browser = await puppeteer.launch();
  const page = await browser.newPage();

  // Navigate the page to a URL
  await page.goto('https://jumpshop-online.com/');

  // Set screen size
  await page.setViewport({width: 1080, height: 1024});

  // Wait for mediaSlider to load
  const searchResultSelector = '.mediaSlider';
  await page.waitForSelector(searchResultSelector);

  // Create images directory if it doesn't exist
  const imagesDir = './images';
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
  }

  // Extract all image URLs from mediaSlider
  const imageUrls = await page.evaluate((selector) => {
    const mediaSlider = document.querySelector(selector);
    if (!mediaSlider) return [];
    
    const images = mediaSlider.querySelectorAll('img');
    return Array.from(images).map(img => img.src).filter(src => src);
  }, searchResultSelector);

  console.log(`Found ${imageUrls.length} images in mediaSlider`);

  // Download each image
  for (let i = 0; i < imageUrls.length; i++) {
    const imageUrl = imageUrls[i];
    try {
      const fileName = `image_${i + 1}_${path.basename(new URL(imageUrl).pathname) || 'image.jpg'}`;
      const filePath = path.join(imagesDir, fileName);
      
      await downloadImage(imageUrl, filePath);
      console.log(`Downloaded: ${fileName}`);
    } catch (error) {
      console.error(`Failed to download image ${i + 1}:`, error.message);
    }
  }

  await browser.close();
})();

// Helper function to download images
function downloadImage(url, filePath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    protocol.get(url, (response) => {
      if (response.statusCode === 200) {
        const fileStream = fs.createWriteStream(filePath);
        response.pipe(fileStream);
        fileStream.on('finish', () => {
          fileStream.close();
          resolve();
        });
        fileStream.on('error', reject);
      } else {
        reject(new Error(`HTTP ${response.statusCode}`));
      }
    }).on('error', reject);
  });
}