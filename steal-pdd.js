import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import fs from 'fs';
import path from 'path';
import https from 'https';
import http from 'http';

(async () => {
  // Launch the browser and open a new blank page
  puppeteer.use(StealthPlugin());

  const browser = await puppeteer.launch({headless: false});
  const page = await browser.newPage();

  // Navigate the page to a URL
  await page.goto('https://mms.pinduoduo.com/login/');

  // Set screen size
  await page.setViewport({width: 1080, height: 1024});

  
  // await page.screenshot({ path: 'stealth.png', fullPage: true })

  // Wait for mediaSlider to load
  const searchResultSelector = '.login-tab .last-item';
  await page.waitForSelector(searchResultSelector);

  // Create images directory if it doesn't exist
  // const imagesDir = './images';
  // if (!fs.existsSync(imagesDir)) {
  //   fs.mkdirSync(imagesDir, { recursive: true });
  // }


  // Extract all image URLs from mediaSlider


  await page.locator(searchResultSelector).click();
  await page.locator("#usernameId").fill('SJ19357181930')
  await page.locator("#passwordId").fill('Chongbeian1')
  await page.locator('.login-info-section button').click();


  // await browser.close();
})();

// Helper function to download images
function downloadImage(url, filePath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    protocol.get(url, (response) => {
      if (response.statusCode === 200) {
        const fileStream = fs.createWriteStream(filePath);
        response.pipe(fileStream);
        fileStream.on('finish', () => {
          fileStream.close();
          resolve();
        });
        fileStream.on('error', reject);
      } else {
        reject(new Error(`HTTP ${response.statusCode}`));
      }
    }).on('error', reject);
  });
}