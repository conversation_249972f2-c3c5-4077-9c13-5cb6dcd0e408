{"name": "@cliqz/adblocker-extended-selectors", "version": "1.34.0", "description": "Ghostery adblocker library (extended CSS selectors implementation)", "author": {"name": "Ghostery"}, "homepage": "https://github.com/ghostery/adblocker#readme", "license": "MPL-2.0", "type": "module", "tshy": {"project": "./tsconfig.json", "exports": {"./package.json": "./package.json", ".": "./src/index.js"}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["LICENSE", "dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+ssh://**************/ghostery/adblocker.git", "directory": "packages/adblocker-extended-selectors"}, "scripts": {"clean": "rimraf dist coverage .tshy .tshy-build .nyc_output .rollup.cache", "lint": "eslint src test", "build": "tshy && rollup --config ./rollup.config.js", "test": "nyc mocha --config ../../.mocharc.json"}, "bugs": {"url": "https://github.com/ghostery/adblocker/issues"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "@types/chai": "^5.0.0", "@types/jsdom": "^21.1.3", "@types/mocha": "^10.0.1", "@types/node": "^22.0.2", "chai": "^5.1.0", "eslint": "^9.3.0", "jsdom": "^25.0.0", "mocha": "^10.2.0", "nyc": "^17.0.0", "rimraf": "^6.0.1", "rollup": "^4.17.2", "tshy": "^3.0.2", "typescript": "^5.5.2"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "gitHead": "a68f58dfe72eb92ebf8ab1836608aa752f0d13e9"}