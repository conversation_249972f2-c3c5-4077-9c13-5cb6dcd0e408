/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
import type { AST } from './types.js';
export declare function matchPattern(pattern: string, text: string): boolean;
export declare function matches(element: Element, selector: AST): boolean;
export declare function querySelectorAll(element: Element, selector: AST): Element[];
//# sourceMappingURL=eval.d.ts.map