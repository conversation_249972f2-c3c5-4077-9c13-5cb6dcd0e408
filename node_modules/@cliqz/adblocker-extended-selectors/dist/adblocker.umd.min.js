!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).adblocker={})}(this,(function(e){"use strict";function t(e){return e.every((e=>"string"!=typeof e))}function n(e){return e.every((e=>"comma"!==e.type&&"combinator"!==e.type))}const o=new Set(["any","dir","has","host-context","if","if-not","is","matches","not","where"]),s={attribute:/\[\s*(?:(?<namespace>\*|[-\w]*)\|)?(?<name>[-\w\u{0080}-\u{FFFF}]+)\s*(?:(?<operator>\W?=)\s*(?<value>.+?)\s*(?<caseSensitive>[iIsS])?\s*)?\]/gu,id:/#(?<name>(?:[-\w\u{0080}-\u{FFFF}]|\\.)+)/gu,class:/\.(?<name>(?:[-\w\u{0080}-\u{FFFF}]|\\.)+)/gu,comma:/\s*,\s*/g,combinator:/\s*[\s>+~]\s*/g,"pseudo-element":/::(?<name>[-\w\u{0080}-\u{FFFF}]+)(?:\((?:¶*)\))?/gu,"pseudo-class":/:(?<name>[-\w\u{0080}-\u{FFFF}]+)(?:\((?<argument>¶*)\))?/gu,type:/(?:(?<namespace>\*|[-\w]*)\|)?(?<name>[-\w\u{0080}-\u{FFFF}]+)|\*/gu},r=new Set(["pseudo-class","pseudo-element"]),i=new Set([...r,"attribute"]),c=new Set(["combinator","comma"]),l=Object.assign({},s);function u(e,t){e.lastIndex=0;const n=e.exec(t);if(null===n)return;const o=n.index-1,s=n[0],r=t.slice(0,o+1),i=t.slice(o+s.length+1);return[r,[s,n.groups||{}],i]}l["pseudo-element"]=RegExp(s["pseudo-element"].source.replace("(?<argument>¶*)","(?<argument>.*?)"),"gu"),l["pseudo-class"]=RegExp(s["pseudo-class"].source.replace("(?<argument>¶*)","(?<argument>.*)"),"gu");const a=[e=>{const t=u(s.attribute,e);if(void 0===t)return;const[n,[o,{name:r,operator:i,value:c,namespace:l,caseSensitive:a}],f]=t;return void 0!==r?[n,{type:"attribute",content:o,length:o.length,namespace:l,caseSensitive:a,pos:[],name:r,operator:i,value:c},f]:void 0},e=>{const t=u(s.id,e);if(void 0===t)return;const[n,[o,{name:r}],i]=t;return void 0!==r?[n,{type:"id",content:o,length:o.length,pos:[],name:r},i]:void 0},e=>{const t=u(s.class,e);if(void 0===t)return;const[n,[o,{name:r}],i]=t;return void 0!==r?[n,{type:"class",content:o,length:o.length,pos:[],name:r},i]:void 0},e=>{const t=u(s.comma,e);if(void 0===t)return;const[n,[o],r]=t;return[n,{type:"comma",content:o,length:o.length,pos:[]},r]},e=>{const t=u(s.combinator,e);if(void 0===t)return;const[n,[o],r]=t;return[n,{type:"combinator",content:o,length:o.length,pos:[]},r]},e=>{const t=u(s["pseudo-element"],e);if(void 0===t)return;const[n,[o,{name:r}],i]=t;return void 0!==r?[n,{type:"pseudo-element",content:o,length:o.length,pos:[],name:r},i]:void 0},e=>{const t=u(s["pseudo-class"],e);if(void 0===t)return;const[n,[o,{name:r,argument:i}],c]=t;return void 0!==r?[n,{type:"pseudo-class",content:o,length:o.length,pos:[],name:r,argument:i,subtree:void 0},c]:void 0},e=>{const t=u(s.type,e);if(void 0===t)return;const[n,[o,{name:r,namespace:i}],c]=t;return[n,{type:"type",content:o,length:o.length,namespace:i,pos:[],name:r},c]}];function f(e,t,n,o){for(const s of t)for(const t of e)if(o.has(t.type)&&t.pos[0]<s.start&&s.start<t.pos[1]){const e=t.content;if(t.content=t.content.replace(n,s.str),t.content!==e){l[t.type].lastIndex=0;const e=l[t.type].exec(t.content);null!==e&&Object.assign(t,e.groups)}}}function p(e,t){let n=0;for(t-=1;t>=0&&"\\"===e[t];)n+=1,t-=1;return n%2!=0}function d(e,t,n){let o=n+1;for(;-1!==(o=e.indexOf(t,o))&&!0===p(e,o);)o+=1;if(-1!==o)return e.slice(n,o+1)}function m(e,t){let n=0;for(let o=t;o<e.length;o++){const s=e[o];if("("===s)n+=1;else if(")"===s){if(!(n>0))return;n-=1}if(0===n)return e.slice(t,o+1)}}function h(e,t,n,o){const s=[];let r=0;for(;-1!==(r=e.indexOf(n,r));){const n=o(e,r);if(void 0===n)break;s.push({str:n,start:r}),e=`${e.slice(0,r+1)}${t.repeat(n.length-2)}${e.slice(r+n.length-1)}`,r+=n.length}return[s,e]}function g(e){if("string"!=typeof e)return[];if(0===(e=e.trim()).length)return[];const[n,o]=h(e,"§",'"',((e,t)=>d(e,'"',t))),[s,l]=h(o,"§","'",((e,t)=>d(e,"'",t))),[u,p]=h(l,"¶","(",m),g=function(e){if(!e)return[];const n=[e];for(const e of a)for(let t=0;t<n.length;t++){const o=n[t];if("string"==typeof o){const s=e(o);void 0!==s&&n.splice(t,1,...s.filter((e=>0!==e.length)))}}let o=0;for(const e of n)"string"!=typeof e&&(e.pos=[o,o+e.length],c.has(e.type)&&(e.content=e.content.trim()||" ")),o+=e.length;return t(n)?n:[]}(p);return f(g,u,/\(¶*\)/,r),f(g,n,/"§*"/,i),f(g,s,/'§*'/,i),g}function y(e,{list:t=!0}={}){if(!0===t&&e.some((e=>"comma"===e.type))){const t=[],n=[];for(let o=0;o<e.length;o+=1){const s=e[o];if("comma"===s.type){if(0===n.length)throw new Error("Incorrect comma at "+o);const e=y(n,{list:!1});void 0!==e&&t.push(e),n.length=0}else n.push(s)}if(0===n.length)throw new Error("Trailing comma");{const e=y(n,{list:!1});void 0!==e&&t.push(e)}return{type:"list",list:t}}for(let t=e.length-1;t>=0;t--){const n=e[t];if("combinator"===n.type){const o=y(e.slice(0,t)),s=y(e.slice(t+1));if(void 0===s)return;if(" "!==n.content&&"~"!==n.content&&"+"!==n.content&&">"!==n.content)return;return{type:"complex",combinator:n.content,left:o,right:s}}}if(0!==e.length)return n(e)?1===e.length?e[0]:{type:"compound",compound:[...e]}:void 0}function v(e,t,n,o){if(void 0!==e){if("complex"===e.type)v(e.left,t,n,e),v(e.right,t,n,e);else if("compound"===e.type)for(const o of e.compound)v(o,t,n,e);else"pseudo-class"===e.type&&void 0!==e.subtree&&void 0!==n&&"pseudo-class"===n.type&&void 0!==n.subtree&&v(e.subtree,t,n,e);t(e,o)}}function b(e,t){if("id"===t.type||"class"===t.type||"type"===t.type||"attribute"===t.type)return e.matches(t.content);if("list"===t.type)return t.list.some((t=>b(e,t)));if("compound"===t.type)return t.compound.every((t=>b(e,t)));if("pseudo-class"===t.type){if("has"===t.name||"if"===t.name)return void 0!==t.subtree&&0!==S(e,t.subtree).length;if("not"===t.name)return void 0!==t.subtree&&!1===b(e,t.subtree);if("has-text"===t.name){const{argument:n}=t;if(void 0===n)return!1;const o=e.textContent;return null!==o&&function(e,t){if(e.startsWith("/")&&(e.endsWith("/")||e.endsWith("/i"))){let n=!0;return(e=e.slice(1)).endsWith("/")?e=e.slice(0,-1):(e=e.slice(0,-2),n=!1),new RegExp(e,!1===n?"i":void 0).test(t)}return t.includes(e)}(n,o)}if("min-text-length"===t.name){const n=Number(t.argument);if(Number.isNaN(n)||n<0)return!1;const o=e.textContent;return null!==o&&o.length>=n}}return!1}function S(e,t){const n=[];if("id"===t.type||"class"===t.type||"type"===t.type||"attribute"===t.type)n.push(...e.querySelectorAll(t.content));else if("list"===t.type)for(const o of t.list)n.push(...S(e,o));else if("compound"===t.type)0!==t.compound.length&&n.push(...S(e,t.compound[0]).filter((e=>t.compound.slice(1).every((t=>b(e,t))))));else if("complex"===t.type){const o=void 0===t.left?[e]:S(e,t.left);if(" "===t.combinator)for(const e of o)n.push(...S(e,t.right));else if(">"===t.combinator)for(const e of o)for(const o of e.children)!0===b(o,t.right)&&n.push(o);else if("~"===t.combinator)for(const e of o){let o=e;for(;null!==(o=o.nextElementSibling);)!0===b(o,t.right)&&n.push(o)}else if("+"===t.combinator)for(const e of o){const o=e.nextElementSibling;null!==o&&!0===b(o,t.right)&&n.push(o)}}else if("pseudo-class"===t.type)for(const o of e.querySelectorAll("*"))!0===b(o,t)&&n.push(o);return n}const x=new Set(["has","has-text","if"]),w=new Set(["active","any","any-link","blank","checked","default","defined","dir","disabled","empty","enabled","first","first-child","first-of-type","focus","focus-visible","focus-within","fullscreen","host","host-context","hover","in-range","indeterminate","invalid","is","lang","last-child","last-of-type","left","link","matches","not","nth-child","nth-last-child","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","placeholder-shown","read-only","read-write","required","right","root","scope","target","valid","visited","where"]),F=new Set(["after","before","first-letter","first-line"]);var E;e.SelectorType=void 0,(E=e.SelectorType||(e.SelectorType={}))[E.Normal=0]="Normal",E[E.Extended=1]="Extended",E[E.Invalid=2]="Invalid",e.EXTENDED_PSEUDO_CLASSES=x,e.PSEUDO_CLASSES=w,e.PSEUDO_ELEMENTS=F,e.classifySelector=function t(n){if(-1===n.indexOf(":"))return e.SelectorType.Normal;const s=g(n);let r=!1;for(const n of s)if("pseudo-class"===n.type){const{name:s}=n;if(!0===x.has(s))r=!0;else if(!1===w.has(s)&&!1===F.has(s))return e.SelectorType.Invalid;if(!1===r&&void 0!==n.argument&&!0===o.has(s)){const o=t(n.argument);if(o===e.SelectorType.Invalid)return o;o===e.SelectorType.Extended&&(r=!0)}}return!0===r?e.SelectorType.Extended:e.SelectorType.Normal},e.isAST=n,e.isAtoms=t,e.matches=b,e.parse=function e(t,{recursive:n=!0,list:s=!0}={}){const r=g(t);if(0===r.length)return;const i=y(r,{list:s});return!0===n&&v(i,(t=>{"pseudo-class"===t.type&&t.argument&&void 0!==t.name&&o.has(t.name)&&(t.subtree=e(t.argument,{recursive:!0,list:!0}))})),i},e.querySelectorAll=S,e.tokenize=g}));
//# sourceMappingURL=adblocker.umd.min.js.map
