{"version": 3, "file": "adblocker.umd.min.js", "sources": ["esm/types.js", "esm/parse.js", "esm/eval.js", "esm/extended.js"], "sourcesContent": ["/*!\n * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.\n *\n * This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/.\n */\nconst tokenTypes = [\n    'attribute',\n    'id',\n    'class',\n    'comma',\n    'combinator',\n    'pseudo-element',\n    'pseudo-class',\n    'type',\n];\nexport function isAtoms(tokens) {\n    return tokens.every((token) => typeof token !== 'string');\n}\nexport function isAST(tokens) {\n    return tokens.every((token) => token.type !== 'comma' && token.type !== 'combinator');\n}\n//# sourceMappingURL=types.js.map", "/*!\n * Based on parsel. Extended by <PERSON><PERSON><PERSON> for Ghostery (2021).\n * https://github.com/LeaVerou/parsel\n *\n * MIT License\n *\n * Copyright (c) 2020 Lea Verou\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nimport { isAST, isAtoms } from './types.js';\nexport const RECURSIVE_PSEUDO_CLASSES = new Set([\n    'any',\n    'dir',\n    'has',\n    'host-context',\n    'if',\n    'if-not',\n    'is',\n    'matches',\n    'not',\n    'where',\n]);\nconst TOKENS = {\n    attribute: /\\[\\s*(?:(?<namespace>\\*|[-\\w]*)\\|)?(?<name>[-\\w\\u{0080}-\\u{FFFF}]+)\\s*(?:(?<operator>\\W?=)\\s*(?<value>.+?)\\s*(?<caseSensitive>[iIsS])?\\s*)?\\]/gu,\n    id: /#(?<name>(?:[-\\w\\u{0080}-\\u{FFFF}]|\\\\.)+)/gu,\n    class: /\\.(?<name>(?:[-\\w\\u{0080}-\\u{FFFF}]|\\\\.)+)/gu,\n    comma: /\\s*,\\s*/g, // must be before combinator\n    combinator: /\\s*[\\s>+~]\\s*/g, // this must be after attribute\n    'pseudo-element': /::(?<name>[-\\w\\u{0080}-\\u{FFFF}]+)(?:\\((?:¶*)\\))?/gu, // this must be before pseudo-class\n    'pseudo-class': /:(?<name>[-\\w\\u{0080}-\\u{FFFF}]+)(?:\\((?<argument>¶*)\\))?/gu,\n    type: /(?:(?<namespace>\\*|[-\\w]*)\\|)?(?<name>[-\\w\\u{0080}-\\u{FFFF}]+)|\\*/gu, // this must be last\n};\nconst TOKENS_WITH_PARENS = new Set(['pseudo-class', 'pseudo-element']);\nconst TOKENS_WITH_STRINGS = new Set([...TOKENS_WITH_PARENS, 'attribute']);\nconst TRIM_TOKENS = new Set(['combinator', 'comma']);\nconst TOKENS_FOR_RESTORE = Object.assign({}, TOKENS);\nTOKENS_FOR_RESTORE['pseudo-element'] = RegExp(TOKENS['pseudo-element'].source.replace('(?<argument>¶*)', '(?<argument>.*?)'), 'gu');\nTOKENS_FOR_RESTORE['pseudo-class'] = RegExp(TOKENS['pseudo-class'].source.replace('(?<argument>¶*)', '(?<argument>.*)'), 'gu');\n// TODO - it feels like with some more typing shenanigans we could replace groups validation by generic logic in this function.\nfunction splitOnMatch(pattern, str) {\n    pattern.lastIndex = 0;\n    const match = pattern.exec(str);\n    if (match === null) {\n        return undefined;\n    }\n    const from = match.index - 1;\n    const content = match[0];\n    const before = str.slice(0, from + 1);\n    const after = str.slice(from + content.length + 1);\n    return [before, [content, match.groups || {}], after];\n}\nconst GRAMMAR = [\n    // attribute\n    (str) => {\n        const match = splitOnMatch(TOKENS.attribute, str);\n        if (match === undefined) {\n            return undefined;\n        }\n        const [before, [content, { name, operator, value, namespace, caseSensitive }], after] = match;\n        if (name === undefined) {\n            return undefined;\n        }\n        return [\n            before,\n            {\n                type: 'attribute',\n                content,\n                length: content.length,\n                namespace,\n                caseSensitive,\n                pos: [],\n                name,\n                operator,\n                value,\n            },\n            after,\n        ];\n    },\n    // #id\n    (str) => {\n        const match = splitOnMatch(TOKENS.id, str);\n        if (match === undefined) {\n            return undefined;\n        }\n        const [before, [content, { name }], after] = match;\n        if (name === undefined) {\n            return undefined;\n        }\n        return [\n            before,\n            {\n                type: 'id',\n                content,\n                length: content.length,\n                pos: [],\n                name,\n            },\n            after,\n        ];\n    },\n    // .class\n    (str) => {\n        const match = splitOnMatch(TOKENS.class, str);\n        if (match === undefined) {\n            return undefined;\n        }\n        const [before, [content, { name }], after] = match;\n        if (name === undefined) {\n            return undefined;\n        }\n        return [\n            before,\n            {\n                type: 'class',\n                content,\n                length: content.length,\n                pos: [],\n                name,\n            },\n            after,\n        ];\n    },\n    // comma ,\n    (str) => {\n        const match = splitOnMatch(TOKENS.comma, str);\n        if (match === undefined) {\n            return undefined;\n        }\n        const [before, [content], after] = match;\n        return [\n            before,\n            {\n                type: 'comma',\n                content,\n                length: content.length,\n                pos: [],\n            },\n            after,\n        ];\n    },\n    // combinator\n    (str) => {\n        const match = splitOnMatch(TOKENS.combinator, str);\n        if (match === undefined) {\n            return undefined;\n        }\n        const [before, [content], after] = match;\n        return [\n            before,\n            {\n                type: 'combinator',\n                content,\n                length: content.length,\n                pos: [],\n            },\n            after,\n        ];\n    },\n    // pseudo-element\n    (str) => {\n        const match = splitOnMatch(TOKENS['pseudo-element'], str);\n        if (match === undefined) {\n            return undefined;\n        }\n        const [before, [content, { name }], after] = match;\n        if (name === undefined) {\n            return undefined;\n        }\n        return [\n            before,\n            {\n                type: 'pseudo-element',\n                content,\n                length: content.length,\n                pos: [],\n                name,\n            },\n            after,\n        ];\n    },\n    // pseudo-class\n    (str) => {\n        const match = splitOnMatch(TOKENS['pseudo-class'], str);\n        if (match === undefined) {\n            return undefined;\n        }\n        // TODO - here `argument` can be undefined and should be rejected?\n        const [before, [content, { name, argument }], after] = match;\n        if (name === undefined) {\n            return undefined;\n        }\n        return [\n            before,\n            {\n                type: 'pseudo-class',\n                content,\n                length: content.length,\n                pos: [],\n                name,\n                argument,\n                subtree: undefined,\n            },\n            after,\n        ];\n    },\n    // type\n    (str) => {\n        const match = splitOnMatch(TOKENS.type, str);\n        if (match === undefined) {\n            return undefined;\n        }\n        const [before, [content, { name, namespace }], after] = match;\n        return [\n            before,\n            {\n                type: 'type',\n                content,\n                length: content.length,\n                namespace,\n                pos: [],\n                name,\n            },\n            after,\n        ];\n    },\n];\nfunction tokenizeBy(text) {\n    if (!text) {\n        return [];\n    }\n    const strarr = [text];\n    for (const tokenizer of GRAMMAR) {\n        for (let i = 0; i < strarr.length; i++) {\n            const str = strarr[i];\n            if (typeof str === 'string') {\n                const match = tokenizer(str);\n                if (match !== undefined) {\n                    strarr.splice(i, 1, ...match.filter((a) => a.length !== 0));\n                }\n            }\n        }\n    }\n    let offset = 0;\n    for (const token of strarr) {\n        if (typeof token !== 'string') {\n            token.pos = [offset, offset + token.length];\n            if (TRIM_TOKENS.has(token.type)) {\n                token.content = token.content.trim() || ' ';\n            }\n        }\n        offset += token.length;\n    }\n    if (isAtoms(strarr)) {\n        return strarr;\n    }\n    // NOTE: here this means that parsing failed.\n    return [];\n}\nfunction restoreNested(tokens, strings, regex, types) {\n    // TODO - here from offsets in strings and tokens we should be able to find the exact spot without RegExp?\n    for (const str of strings) {\n        for (const token of tokens) {\n            if (types.has(token.type) && token.pos[0] < str.start && str.start < token.pos[1]) {\n                const content = token.content;\n                token.content = token.content.replace(regex, str.str);\n                if (token.content !== content) {\n                    // actually changed?\n                    // Re-evaluate groups\n                    TOKENS_FOR_RESTORE[token.type].lastIndex = 0;\n                    const match = TOKENS_FOR_RESTORE[token.type].exec(token.content);\n                    if (match !== null) {\n                        Object.assign(token, match.groups);\n                    }\n                }\n            }\n        }\n    }\n}\nexport function isEscaped(str, index) {\n    let backslashes = 0;\n    index -= 1;\n    while (index >= 0 && str[index] === '\\\\') {\n        backslashes += 1;\n        index -= 1;\n    }\n    return backslashes % 2 !== 0;\n}\nexport function gobbleQuotes(text, quote, start) {\n    // Find end of quote, taking care of ignoring escaped quotes\n    let end = start + 1;\n    while ((end = text.indexOf(quote, end)) !== -1 && isEscaped(text, end) === true) {\n        end += 1;\n    }\n    if (end === -1) {\n        // Opening quote without closing quote\n        return undefined;\n    }\n    return text.slice(start, end + 1);\n}\nexport function gobbleParens(text, start) {\n    let stack = 0;\n    for (let i = start; i < text.length; i++) {\n        const char = text[i];\n        if (char === '(') {\n            stack += 1;\n        }\n        else if (char === ')') {\n            if (stack > 0) {\n                stack -= 1;\n            }\n            else {\n                // Closing paren without opening paren\n                return undefined;\n            }\n        }\n        if (stack === 0) {\n            return text.slice(start, i + 1);\n        }\n    }\n    // Opening paren without closing paren\n    return undefined;\n}\nexport function replace(selector, replacement, opening, gobble) {\n    const strings = [];\n    let offset = 0;\n    while ((offset = selector.indexOf(opening, offset)) !== -1) {\n        const str = gobble(selector, offset);\n        if (str === undefined) {\n            break;\n        }\n        strings.push({ str, start: offset });\n        selector = `${selector.slice(0, offset + 1)}${replacement.repeat(str.length - 2)}${selector.slice(offset + str.length - 1)}`;\n        offset += str.length;\n    }\n    return [strings, selector];\n}\nexport function tokenize(selector) {\n    if (typeof selector !== 'string') {\n        return [];\n    }\n    // Prevent leading/trailing whitespace be interpreted as combinators\n    selector = selector.trim();\n    if (selector.length === 0) {\n        return [];\n    }\n    // Replace strings with whitespace strings (to preserve offsets)\n    const [doubleQuotes, selectorWithoutDoubleQuotes] = replace(selector, '§', '\"', (text, start) => gobbleQuotes(text, '\"', start));\n    const [singleQuotes, selectorWithoutQuotes] = replace(selectorWithoutDoubleQuotes, '§', \"'\", (text, start) => gobbleQuotes(text, \"'\", start));\n    // Now that strings are out of the way, extract parens and replace them with parens with whitespace (to preserve offsets)\n    const [parens, selectorWithoutParens] = replace(selectorWithoutQuotes, '¶', '(', gobbleParens);\n    // Now we have no nested structures and we can parse with regexes\n    const tokens = tokenizeBy(selectorWithoutParens);\n    // Now restore parens and strings in reverse order\n    restoreNested(tokens, parens, /\\(¶*\\)/, TOKENS_WITH_PARENS);\n    restoreNested(tokens, doubleQuotes, /\"§*\"/, TOKENS_WITH_STRINGS);\n    restoreNested(tokens, singleQuotes, /'§*'/, TOKENS_WITH_STRINGS);\n    return tokens;\n}\n// Convert a flat list of tokens into a tree of complex & compound selectors\nfunction nestTokens(tokens, { list = true } = {}) {\n    if (list === true && tokens.some((t) => t.type === 'comma')) {\n        const selectors = [];\n        const temp = [];\n        for (let i = 0; i < tokens.length; i += 1) {\n            const token = tokens[i];\n            if (token.type === 'comma') {\n                if (temp.length === 0) {\n                    throw new Error('Incorrect comma at ' + i);\n                }\n                const sub = nestTokens(temp, { list: false });\n                if (sub !== undefined) {\n                    selectors.push(sub);\n                }\n                temp.length = 0;\n            }\n            else {\n                temp.push(token);\n            }\n        }\n        if (temp.length === 0) {\n            throw new Error('Trailing comma');\n        }\n        else {\n            const sub = nestTokens(temp, { list: false });\n            if (sub !== undefined) {\n                selectors.push(sub);\n            }\n        }\n        return { type: 'list', list: selectors };\n    }\n    for (let i = tokens.length - 1; i >= 0; i--) {\n        const token = tokens[i];\n        if (token.type === 'combinator') {\n            const left = nestTokens(tokens.slice(0, i));\n            const right = nestTokens(tokens.slice(i + 1));\n            if (right === undefined) {\n                return undefined;\n            }\n            if (token.content !== ' ' &&\n                token.content !== '~' &&\n                token.content !== '+' &&\n                token.content !== '>') {\n                return undefined;\n            }\n            return {\n                type: 'complex',\n                combinator: token.content,\n                left,\n                right,\n            };\n        }\n    }\n    if (tokens.length === 0) {\n        return undefined;\n    }\n    if (isAST(tokens)) {\n        if (tokens.length === 1) {\n            return tokens[0];\n        }\n        // If we're here, there are no combinators, so it's just a list\n        return {\n            type: 'compound',\n            compound: [...tokens], // clone to avoid pointers messing up the AST\n        };\n    }\n    return undefined;\n}\n// Traverse an AST (or part thereof), in depth-first order\nfunction walk(node, callback, o, parent) {\n    if (node === undefined) {\n        return;\n    }\n    if (node.type === 'complex') {\n        walk(node.left, callback, o, node);\n        walk(node.right, callback, o, node);\n    }\n    else if (node.type === 'compound') {\n        for (const n of node.compound) {\n            walk(n, callback, o, node);\n        }\n    }\n    else if (node.type === 'pseudo-class' &&\n        node.subtree !== undefined &&\n        o !== undefined &&\n        o.type === 'pseudo-class' &&\n        o.subtree !== undefined) {\n        walk(node.subtree, callback, o, node);\n    }\n    callback(node, parent);\n}\n/**\n * Parse a CSS selector\n * @param selector {String} The selector to parse\n * @param options.recursive {Boolean} Whether to parse the arguments of pseudo-classes like :is(), :has() etc. Defaults to true.\n * @param options.list {Boolean} Whether this can be a selector list (A, B, C etc). Defaults to true.\n */\nexport function parse(selector, { recursive = true, list = true } = {}) {\n    const tokens = tokenize(selector);\n    if (tokens.length === 0) {\n        return undefined;\n    }\n    const ast = nestTokens(tokens, { list });\n    if (recursive === true) {\n        walk(ast, (node) => {\n            if (node.type === 'pseudo-class' &&\n                node.argument &&\n                node.name !== undefined &&\n                RECURSIVE_PSEUDO_CLASSES.has(node.name)) {\n                node.subtree = parse(node.argument, { recursive: true, list: true });\n            }\n        });\n    }\n    return ast;\n}\n//# sourceMappingURL=parse.js.map", "/*!\n * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.\n *\n * This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/.\n */\nexport function matchPattern(pattern, text) {\n    // TODO - support 'm' RegExp argument\n    if (pattern.startsWith('/') && (pattern.endsWith('/') || pattern.endsWith('/i'))) {\n        let caseSensitive = true;\n        pattern = pattern.slice(1);\n        if (pattern.endsWith('/')) {\n            pattern = pattern.slice(0, -1);\n        }\n        else {\n            pattern = pattern.slice(0, -2);\n            caseSensitive = false;\n        }\n        return new RegExp(pattern, caseSensitive === false ? 'i' : undefined).test(text);\n    }\n    return text.includes(pattern);\n}\nexport function matches(element, selector) {\n    if (selector.type === 'id' ||\n        selector.type === 'class' ||\n        selector.type === 'type' ||\n        selector.type === 'attribute') {\n        return element.matches(selector.content);\n    }\n    else if (selector.type === 'list') {\n        return selector.list.some((s) => matches(element, s));\n    }\n    else if (selector.type === 'compound') {\n        return selector.compound.every((s) => matches(element, s));\n    }\n    else if (selector.type === 'pseudo-class') {\n        if (selector.name === 'has' || selector.name === 'if') {\n            // TODO - is this a querySelectorAll or matches here?\n            return (selector.subtree !== undefined && querySelectorAll(element, selector.subtree).length !== 0);\n        }\n        else if (selector.name === 'not') {\n            return selector.subtree !== undefined && matches(element, selector.subtree) === false;\n        }\n        else if (selector.name === 'has-text') {\n            const { argument } = selector;\n            if (argument === undefined) {\n                return false;\n            }\n            const text = element.textContent;\n            if (text === null) {\n                return false;\n            }\n            return matchPattern(argument, text);\n        }\n        else if (selector.name === 'min-text-length') {\n            const minLength = Number(selector.argument);\n            if (Number.isNaN(minLength) || minLength < 0) {\n                return false;\n            }\n            const text = element.textContent;\n            if (text === null) {\n                return false;\n            }\n            return text.length >= minLength;\n        }\n    }\n    return false;\n}\nexport function querySelectorAll(element, selector) {\n    const elements = [];\n    if (selector.type === 'id' ||\n        selector.type === 'class' ||\n        selector.type === 'type' ||\n        selector.type === 'attribute') {\n        elements.push(...element.querySelectorAll(selector.content));\n    }\n    else if (selector.type === 'list') {\n        for (const subSelector of selector.list) {\n            elements.push(...querySelectorAll(element, subSelector));\n        }\n    }\n    else if (selector.type === 'compound') {\n        // TODO - handling compound needs to be reworked...\n        // .cls:upward(1) for example will not work with this implementation.\n        // :upward is not about selecting, but transforming a set of nodes (i.e.\n        // uBO's transpose method).\n        if (selector.compound.length !== 0) {\n            elements.push(...querySelectorAll(element, selector.compound[0]).filter((e) => selector.compound.slice(1).every((s) => matches(e, s))));\n        }\n    }\n    else if (selector.type === 'complex') {\n        const elements2 = selector.left === undefined ? [element] : querySelectorAll(element, selector.left);\n        if (selector.combinator === ' ') {\n            for (const element2 of elements2) {\n                elements.push(...querySelectorAll(element2, selector.right));\n            }\n        }\n        else if (selector.combinator === '>') {\n            for (const element2 of elements2) {\n                for (const child of element2.children) {\n                    if (matches(child, selector.right) === true) {\n                        elements.push(child);\n                    }\n                }\n            }\n        }\n        else if (selector.combinator === '~') {\n            for (const element2 of elements2) {\n                let sibling = element2;\n                while ((sibling = sibling.nextElementSibling) !== null) {\n                    if (matches(sibling, selector.right) === true) {\n                        elements.push(sibling);\n                    }\n                }\n            }\n        }\n        else if (selector.combinator === '+') {\n            for (const element2 of elements2) {\n                const nextElementSibling = element2.nextElementSibling;\n                if (nextElementSibling !== null && matches(nextElementSibling, selector.right) === true) {\n                    elements.push(nextElementSibling);\n                }\n            }\n        }\n    }\n    else if (selector.type === 'pseudo-class') {\n        // if (selector.name === 'upward') {\n        //   let n = Number(selector.argument);\n        //   console.log('upward', selector, n);\n        //   if (Number.isNaN(n) === false) {\n        //     if (n >= 1 && n < 256) {\n        //       let ancestor: Element | null = element;\n        //       while (ancestor !== null && n > 0) {\n        //         ancestor = ancestor.parentElement;\n        //         n -= 1;\n        //       }\n        //       if (ancestor !== null && n === 0) {\n        //         elements.push(element);\n        //       }\n        //     }\n        //   } else if (selector.argument !== undefined) {\n        //     const parent = element.parentElement;\n        //     if (parent !== null) {\n        //       const ancestor = parent.closest(selector.argument);\n        //       if (ancestor !== null) {\n        //         elements.push(ancestor);\n        //       }\n        //     }\n        //   }\n        // } else {\n        for (const subElement of element.querySelectorAll('*')) {\n            if (matches(subElement, selector) === true) {\n                elements.push(subElement);\n            }\n        }\n        // }\n    }\n    return elements;\n}\n//# sourceMappingURL=eval.js.map", "/*!\n * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.\n *\n * This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/.\n */\nimport { tokenize, RECURSIVE_PSEUDO_CLASSES } from './parse.js';\nexport const EXTENDED_PSEUDO_CLASSES = new Set([\n    // '-abp-contains',\n    // '-abp-has',\n    // '-abp-properties',\n    'has',\n    'has-text',\n    'if',\n    // 'if-not',\n    // 'matches-css',\n    // 'matches-css-after',\n    // 'matches-css-before',\n    // 'min-text-length',\n    // 'nth-ancestor',\n    // 'upward',\n    // 'watch-attr',\n    // 'watch-attrs',\n    // 'xpath',\n]);\nexport const PSEUDO_CLASSES = new Set([\n    'active',\n    'any',\n    'any-link',\n    'blank',\n    'checked',\n    'default',\n    'defined',\n    'dir',\n    'disabled',\n    'empty',\n    'enabled',\n    'first',\n    'first-child',\n    'first-of-type',\n    'focus',\n    'focus-visible',\n    'focus-within',\n    'fullscreen',\n    'host',\n    'host-context',\n    'hover',\n    'in-range',\n    'indeterminate',\n    'invalid',\n    'is',\n    'lang',\n    'last-child',\n    'last-of-type',\n    'left',\n    'link',\n    'matches',\n    // NOTE: by default we consider `:not(...)` to be a normal CSS selector since,\n    // we are only interested in cases where the argument is an extended selector.\n    // If that is the case, it will still be detected as such.\n    'not',\n    'nth-child',\n    'nth-last-child',\n    'nth-last-of-type',\n    'nth-of-type',\n    'only-child',\n    'only-of-type',\n    'optional',\n    'out-of-range',\n    'placeholder-shown',\n    'read-only',\n    'read-write',\n    'required',\n    'right',\n    'root',\n    'scope',\n    'target',\n    'valid',\n    'visited',\n    'where',\n]);\n// NOTE: here we only need to list the pseudo-elements which can appear with a\n// single colon (e.g. :after or ::after are valid for backward compatibility\n// reasons). They can be misinterpreted as pseudo-classes by the tokenizer for\n// this reason.\nexport const PSEUDO_ELEMENTS = new Set(['after', 'before', 'first-letter', 'first-line']);\nexport var SelectorType;\n(function (SelectorType) {\n    SelectorType[SelectorType[\"Normal\"] = 0] = \"Normal\";\n    SelectorType[SelectorType[\"Extended\"] = 1] = \"Extended\";\n    SelectorType[SelectorType[\"Invalid\"] = 2] = \"Invalid\";\n})(SelectorType || (SelectorType = {}));\nexport function classifySelector(selector) {\n    // In most cases there is no pseudo-anything so we can quickly exit.\n    if (selector.indexOf(':') === -1) {\n        return SelectorType.Normal;\n    }\n    const tokens = tokenize(selector);\n    // Detect pseudo-classes\n    let foundSupportedExtendedSelector = false;\n    for (const token of tokens) {\n        if (token.type === 'pseudo-class') {\n            const { name } = token;\n            if (EXTENDED_PSEUDO_CLASSES.has(name) === true) {\n                foundSupportedExtendedSelector = true;\n            }\n            else if (PSEUDO_CLASSES.has(name) === false && PSEUDO_ELEMENTS.has(name) === false) {\n                return SelectorType.Invalid;\n            }\n            // Recursively\n            if (foundSupportedExtendedSelector === false &&\n                token.argument !== undefined &&\n                RECURSIVE_PSEUDO_CLASSES.has(name) === true) {\n                const argumentType = classifySelector(token.argument);\n                if (argumentType === SelectorType.Invalid) {\n                    return argumentType;\n                }\n                else if (argumentType === SelectorType.Extended) {\n                    foundSupportedExtendedSelector = true;\n                }\n            }\n        }\n    }\n    if (foundSupportedExtendedSelector === true) {\n        return SelectorType.Extended;\n    }\n    return SelectorType.Normal;\n}\n//# sourceMappingURL=extended.js.map"], "names": ["isA<PERSON>s", "tokens", "every", "token", "isAST", "type", "RECURSIVE_PSEUDO_CLASSES", "Set", "TOKENS", "attribute", "id", "class", "comma", "combinator", "TOKENS_WITH_PARENS", "TOKENS_WITH_STRINGS", "TRIM_TOKENS", "TOKENS_FOR_RESTORE", "Object", "assign", "splitOnMatch", "pattern", "str", "lastIndex", "match", "exec", "from", "index", "content", "before", "slice", "after", "length", "groups", "RegExp", "source", "replace", "GRAMMAR", "undefined", "name", "operator", "value", "namespace", "caseSensitive", "pos", "argument", "subtree", "restoreNested", "strings", "regex", "types", "has", "start", "isEscaped", "backslashes", "gobbleQuotes", "text", "quote", "end", "indexOf", "gobbleParens", "stack", "i", "char", "selector", "replacement", "opening", "gobble", "offset", "push", "repeat", "tokenize", "trim", "doubleQuotes", "selectorWithoutDoubleQuotes", "singleQuotes", "selectorWithoutQuotes", "parens", "selectorWithoutParens", "strarr", "tokenizer", "splice", "filter", "a", "tokenizeBy", "nestTokens", "list", "some", "t", "selectors", "temp", "Error", "sub", "left", "right", "compound", "walk", "node", "callback", "o", "parent", "n", "matches", "element", "s", "querySelectorAll", "textContent", "startsWith", "endsWith", "test", "includes", "matchPattern", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "elements", "subSelector", "e", "elements2", "element2", "child", "children", "sibling", "nextElement<PERSON><PERSON>ling", "subElement", "EXTENDED_PSEUDO_CLASSES", "PSEUDO_CLASSES", "PSEUDO_ELEMENTS", "SelectorType", "exports", "classifySelector", "Normal", "foundSupportedExtendedSelector", "Invalid", "argumentType", "Extended", "parse", "recursive", "ast"], "mappings": "iPAiBO,SAASA,EAAQC,GACpB,OAAOA,EAAOC,OAAOC,GAA2B,iBAAVA,GAC1C,CACO,SAASC,EAAMH,GAClB,OAAOA,EAAOC,OAAOC,GAAyB,UAAfA,EAAME,MAAmC,eAAfF,EAAME,MACnE,CCKO,MAAMC,EAA2B,IAAIC,IAAI,CAC5C,MACA,MACA,MACA,eACA,KACA,SACA,KACA,UACA,MACA,UAEEC,EAAS,CACXC,UAAW,kJACXC,GAAI,8CACJC,MAAO,+CACPC,MAAO,WACPC,WAAY,iBACZ,iBAAkB,sDAClB,eAAgB,8DAChBR,KAAM,uEAEJS,EAAqB,IAAIP,IAAI,CAAC,eAAgB,mBAC9CQ,EAAsB,IAAIR,IAAI,IAAIO,EAAoB,cACtDE,EAAc,IAAIT,IAAI,CAAC,aAAc,UACrCU,EAAqBC,OAAOC,OAAO,CAAE,EAAEX,GAI7C,SAASY,EAAaC,EAASC,GAC3BD,EAAQE,UAAY,EACpB,MAAMC,EAAQH,EAAQI,KAAKH,GAC3B,GAAc,OAAVE,EACA,OAEJ,MAAME,EAAOF,EAAMG,MAAQ,EACrBC,EAAUJ,EAAM,GAChBK,EAASP,EAAIQ,MAAM,EAAGJ,EAAO,GAC7BK,EAAQT,EAAIQ,MAAMJ,EAAOE,EAAQI,OAAS,GAChD,MAAO,CAACH,EAAQ,CAACD,EAASJ,EAAMS,QAAU,CAAE,GAAGF,EACnD,CAdAd,EAAmB,kBAAoBiB,OAAO1B,EAAO,kBAAkB2B,OAAOC,QAAQ,kBAAmB,oBAAqB,MAC9HnB,EAAmB,gBAAkBiB,OAAO1B,EAAO,gBAAgB2B,OAAOC,QAAQ,kBAAmB,mBAAoB,MAczH,MAAMC,EAAU,CAEXf,IACG,MAAME,EAAQJ,EAAaZ,EAAOC,UAAWa,GAC7C,QAAcgB,IAAVd,EACA,OAEJ,MAAOK,GAASD,GAASW,KAAEA,EAAIC,SAAEA,EAAQC,MAAEA,EAAKC,UAAEA,EAASC,cAAEA,IAAkBZ,GAASP,EACxF,YAAac,IAATC,EAGG,CACHV,EACA,CACIxB,KAAM,YACNuB,UACAI,OAAQJ,EAAQI,OAChBU,YACAC,gBACAC,IAAK,GACLL,OACAC,WACAC,SAEJV,QAhBJ,CAiBC,EAGJT,IACG,MAAME,EAAQJ,EAAaZ,EAAOE,GAAIY,GACtC,QAAcgB,IAAVd,EACA,OAEJ,MAAOK,GAASD,GAASW,KAAEA,IAASR,GAASP,EAC7C,YAAac,IAATC,EAGG,CACHV,EACA,CACIxB,KAAM,KACNuB,UACAI,OAAQJ,EAAQI,OAChBY,IAAK,GACLL,QAEJR,QAZJ,CAaC,EAGJT,IACG,MAAME,EAAQJ,EAAaZ,EAAOG,MAAOW,GACzC,QAAcgB,IAAVd,EACA,OAEJ,MAAOK,GAASD,GAASW,KAAEA,IAASR,GAASP,EAC7C,YAAac,IAATC,EAGG,CACHV,EACA,CACIxB,KAAM,QACNuB,UACAI,OAAQJ,EAAQI,OAChBY,IAAK,GACLL,QAEJR,QAZJ,CAaC,EAGJT,IACG,MAAME,EAAQJ,EAAaZ,EAAOI,MAAOU,GACzC,QAAcgB,IAAVd,EACA,OAEJ,MAAOK,GAASD,GAAUG,GAASP,EACnC,MAAO,CACHK,EACA,CACIxB,KAAM,QACNuB,UACAI,OAAQJ,EAAQI,OAChBY,IAAK,IAETb,EACH,EAGJT,IACG,MAAME,EAAQJ,EAAaZ,EAAOK,WAAYS,GAC9C,QAAcgB,IAAVd,EACA,OAEJ,MAAOK,GAASD,GAAUG,GAASP,EACnC,MAAO,CACHK,EACA,CACIxB,KAAM,aACNuB,UACAI,OAAQJ,EAAQI,OAChBY,IAAK,IAETb,EACH,EAGJT,IACG,MAAME,EAAQJ,EAAaZ,EAAO,kBAAmBc,GACrD,QAAcgB,IAAVd,EACA,OAEJ,MAAOK,GAASD,GAASW,KAAEA,IAASR,GAASP,EAC7C,YAAac,IAATC,EAGG,CACHV,EACA,CACIxB,KAAM,iBACNuB,UACAI,OAAQJ,EAAQI,OAChBY,IAAK,GACLL,QAEJR,QAZJ,CAaC,EAGJT,IACG,MAAME,EAAQJ,EAAaZ,EAAO,gBAAiBc,GACnD,QAAcgB,IAAVd,EACA,OAGJ,MAAOK,GAASD,GAASW,KAAEA,EAAIM,SAAEA,IAAad,GAASP,EACvD,YAAac,IAATC,EAGG,CACHV,EACA,CACIxB,KAAM,eACNuB,UACAI,OAAQJ,EAAQI,OAChBY,IAAK,GACLL,OACAM,WACAC,aAASR,GAEbP,QAdJ,CAeC,EAGJT,IACG,MAAME,EAAQJ,EAAaZ,EAAOH,KAAMiB,GACxC,QAAcgB,IAAVd,EACA,OAEJ,MAAOK,GAASD,GAASW,KAAEA,EAAIG,UAAEA,IAAcX,GAASP,EACxD,MAAO,CACHK,EACA,CACIxB,KAAM,OACNuB,UACAI,OAAQJ,EAAQI,OAChBU,YACAE,IAAK,GACLL,QAEJR,EACH,GAmCT,SAASgB,EAAc9C,EAAQ+C,EAASC,EAAOC,GAE3C,IAAK,MAAM5B,KAAO0B,EACd,IAAK,MAAM7C,KAASF,EAChB,GAAIiD,EAAMC,IAAIhD,EAAME,OAASF,EAAMyC,IAAI,GAAKtB,EAAI8B,OAAS9B,EAAI8B,MAAQjD,EAAMyC,IAAI,GAAI,CAC/E,MAAMhB,EAAUzB,EAAMyB,QAEtB,GADAzB,EAAMyB,QAAUzB,EAAMyB,QAAQQ,QAAQa,EAAO3B,EAAIA,KAC7CnB,EAAMyB,UAAYA,EAAS,CAG3BX,EAAmBd,EAAME,MAAMkB,UAAY,EAC3C,MAAMC,EAAQP,EAAmBd,EAAME,MAAMoB,KAAKtB,EAAMyB,SAC1C,OAAVJ,GACAN,OAAOC,OAAOhB,EAAOqB,EAAMS,OAElC,CACJ,CAGb,CACO,SAASoB,EAAU/B,EAAKK,GAC3B,IAAI2B,EAAc,EAElB,IADA3B,GAAS,EACFA,GAAS,GAAoB,OAAfL,EAAIK,IACrB2B,GAAe,EACf3B,GAAS,EAEb,OAAO2B,EAAc,GAAM,CAC/B,CACO,SAASC,EAAaC,EAAMC,EAAOL,GAEtC,IAAIM,EAAMN,EAAQ,EAClB,MAA6C,KAArCM,EAAMF,EAAKG,QAAQF,EAAOC,MAAyC,IAAzBL,EAAUG,EAAME,IAC9DA,GAAO,EAEX,IAAa,IAATA,EAIJ,OAAOF,EAAK1B,MAAMsB,EAAOM,EAAM,EACnC,CACO,SAASE,EAAaJ,EAAMJ,GAC/B,IAAIS,EAAQ,EACZ,IAAK,IAAIC,EAAIV,EAAOU,EAAIN,EAAKxB,OAAQ8B,IAAK,CACtC,MAAMC,EAAOP,EAAKM,GAClB,GAAa,MAATC,EACAF,GAAS,OAER,GAAa,MAATE,EAAc,CACnB,KAAIF,EAAQ,GAKR,OAJAA,GAAS,CAMhB,CACD,GAAc,IAAVA,EACA,OAAOL,EAAK1B,MAAMsB,EAAOU,EAAI,EAEpC,CAGL,CACO,SAAS1B,EAAQ4B,EAAUC,EAAaC,EAASC,GACpD,MAAMnB,EAAU,GAChB,IAAIoB,EAAS,EACb,MAAyD,KAAjDA,EAASJ,EAASL,QAAQO,EAASE,KAAiB,CACxD,MAAM9C,EAAM6C,EAAOH,EAAUI,GAC7B,QAAY9B,IAARhB,EACA,MAEJ0B,EAAQqB,KAAK,CAAE/C,MAAK8B,MAAOgB,IAC3BJ,EAAW,GAAGA,EAASlC,MAAM,EAAGsC,EAAS,KAAKH,EAAYK,OAAOhD,EAAIU,OAAS,KAAKgC,EAASlC,MAAMsC,EAAS9C,EAAIU,OAAS,KACxHoC,GAAU9C,EAAIU,MACjB,CACD,MAAO,CAACgB,EAASgB,EACrB,CACO,SAASO,EAASP,GACrB,GAAwB,iBAAbA,EACP,MAAO,GAIX,GAAwB,KADxBA,EAAWA,EAASQ,QACPxC,OACT,MAAO,GAGX,MAAOyC,EAAcC,GAA+BtC,EAAQ4B,EAAU,IAAK,KAAK,CAACR,EAAMJ,IAAUG,EAAaC,EAAM,IAAKJ,MAClHuB,EAAcC,GAAyBxC,EAAQsC,EAA6B,IAAK,KAAK,CAAClB,EAAMJ,IAAUG,EAAaC,EAAM,IAAKJ,MAE/HyB,EAAQC,GAAyB1C,EAAQwC,EAAuB,IAAK,IAAKhB,GAE3E3D,EA7HV,SAAoBuD,GAChB,IAAKA,EACD,MAAO,GAEX,MAAMuB,EAAS,CAACvB,GAChB,IAAK,MAAMwB,KAAa3C,EACpB,IAAK,IAAIyB,EAAI,EAAGA,EAAIiB,EAAO/C,OAAQ8B,IAAK,CACpC,MAAMxC,EAAMyD,EAAOjB,GACnB,GAAmB,iBAARxC,EAAkB,CACzB,MAAME,EAAQwD,EAAU1D,QACVgB,IAAVd,GACAuD,EAAOE,OAAOnB,EAAG,KAAMtC,EAAM0D,QAAQC,GAAmB,IAAbA,EAAEnD,SAEpD,CACJ,CAEL,IAAIoC,EAAS,EACb,IAAK,MAAMjE,KAAS4E,EACK,iBAAV5E,IACPA,EAAMyC,IAAM,CAACwB,EAAQA,EAASjE,EAAM6B,QAChChB,EAAYmC,IAAIhD,EAAME,QACtBF,EAAMyB,QAAUzB,EAAMyB,QAAQ4C,QAAU,MAGhDJ,GAAUjE,EAAM6B,OAEpB,OAAIhC,EAAQ+E,GACDA,EAGJ,EACX,CA8FmBK,CAAWN,GAK1B,OAHA/B,EAAc9C,EAAQ4E,EAAQ,SAAU/D,GACxCiC,EAAc9C,EAAQwE,EAAc,OAAQ1D,GAC5CgC,EAAc9C,EAAQ0E,EAAc,OAAQ5D,GACrCd,CACX,CAEA,SAASoF,EAAWpF,GAAQqF,KAAEA,GAAO,GAAS,CAAA,GAC1C,IAAa,IAATA,GAAiBrF,EAAOsF,MAAMC,GAAiB,UAAXA,EAAEnF,OAAmB,CACzD,MAAMoF,EAAY,GACZC,EAAO,GACb,IAAK,IAAI5B,EAAI,EAAGA,EAAI7D,EAAO+B,OAAQ8B,GAAK,EAAG,CACvC,MAAM3D,EAAQF,EAAO6D,GACrB,GAAmB,UAAf3D,EAAME,KAAkB,CACxB,GAAoB,IAAhBqF,EAAK1D,OACL,MAAM,IAAI2D,MAAM,sBAAwB7B,GAE5C,MAAM8B,EAAMP,EAAWK,EAAM,CAAEJ,MAAM,SACzBhD,IAARsD,GACAH,EAAUpB,KAAKuB,GAEnBF,EAAK1D,OAAS,CACjB,MAEG0D,EAAKrB,KAAKlE,EAEjB,CACD,GAAoB,IAAhBuF,EAAK1D,OACL,MAAM,IAAI2D,MAAM,kBAEf,CACD,MAAMC,EAAMP,EAAWK,EAAM,CAAEJ,MAAM,SACzBhD,IAARsD,GACAH,EAAUpB,KAAKuB,EAEtB,CACD,MAAO,CAAEvF,KAAM,OAAQiF,KAAMG,EAChC,CACD,IAAK,IAAI3B,EAAI7D,EAAO+B,OAAS,EAAG8B,GAAK,EAAGA,IAAK,CACzC,MAAM3D,EAAQF,EAAO6D,GACrB,GAAmB,eAAf3D,EAAME,KAAuB,CAC7B,MAAMwF,EAAOR,EAAWpF,EAAO6B,MAAM,EAAGgC,IAClCgC,EAAQT,EAAWpF,EAAO6B,MAAMgC,EAAI,IAC1C,QAAcxB,IAAVwD,EACA,OAEJ,GAAsB,MAAlB3F,EAAMyB,SACY,MAAlBzB,EAAMyB,SACY,MAAlBzB,EAAMyB,SACY,MAAlBzB,EAAMyB,QACN,OAEJ,MAAO,CACHvB,KAAM,UACNQ,WAAYV,EAAMyB,QAClBiE,OACAC,QAEP,CACJ,CACD,GAAsB,IAAlB7F,EAAO+B,OAGX,OAAI5B,EAAMH,GACgB,IAAlBA,EAAO+B,OACA/B,EAAO,GAGX,CACHI,KAAM,WACN0F,SAAU,IAAI9F,SAPtB,CAWJ,CAEA,SAAS+F,EAAKC,EAAMC,EAAUC,EAAGC,GAC7B,QAAa9D,IAAT2D,EAAJ,CAGA,GAAkB,YAAdA,EAAK5F,KACL2F,EAAKC,EAAKJ,KAAMK,EAAUC,EAAGF,GAC7BD,EAAKC,EAAKH,MAAOI,EAAUC,EAAGF,QAE7B,GAAkB,aAAdA,EAAK5F,KACV,IAAK,MAAMgG,KAAKJ,EAAKF,SACjBC,EAAKK,EAAGH,EAAUC,EAAGF,OAGN,iBAAdA,EAAK5F,WACOiC,IAAjB2D,EAAKnD,cACCR,IAAN6D,GACW,iBAAXA,EAAE9F,WACYiC,IAAd6D,EAAErD,SACFkD,EAAKC,EAAKnD,QAASoD,EAAUC,EAAGF,GAEpCC,EAASD,EAAMG,EAjBd,CAkBL,CC3bO,SAASE,EAAQC,EAASvC,GAC7B,GAAsB,OAAlBA,EAAS3D,MACS,UAAlB2D,EAAS3D,MACS,SAAlB2D,EAAS3D,MACS,cAAlB2D,EAAS3D,KACT,OAAOkG,EAAQD,QAAQtC,EAASpC,SAE/B,GAAsB,SAAlBoC,EAAS3D,KACd,OAAO2D,EAASsB,KAAKC,MAAMiB,GAAMF,EAAQC,EAASC,KAEjD,GAAsB,aAAlBxC,EAAS3D,KACd,OAAO2D,EAAS+B,SAAS7F,OAAOsG,GAAMF,EAAQC,EAASC,KAEtD,GAAsB,iBAAlBxC,EAAS3D,KAAyB,CACvC,GAAsB,QAAlB2D,EAASzB,MAAoC,OAAlByB,EAASzB,KAEpC,YAA6BD,IAArB0B,EAASlB,SAAgF,IAAvD2D,EAAiBF,EAASvC,EAASlB,SAASd,OAErF,GAAsB,QAAlBgC,EAASzB,KACd,YAA4BD,IAArB0B,EAASlB,UAAgE,IAAvCwD,EAAQC,EAASvC,EAASlB,SAElE,GAAsB,aAAlBkB,EAASzB,KAAqB,CACnC,MAAMM,SAAEA,GAAamB,EACrB,QAAiB1B,IAAbO,EACA,OAAO,EAEX,MAAMW,EAAO+C,EAAQG,YACrB,OAAa,OAATlD,GA3CT,SAAsBnC,EAASmC,GAElC,GAAInC,EAAQsF,WAAW,OAAStF,EAAQuF,SAAS,MAAQvF,EAAQuF,SAAS,OAAQ,CAC9E,IAAIjE,GAAgB,EASpB,OARAtB,EAAUA,EAAQS,MAAM,IACZ8E,SAAS,KACjBvF,EAAUA,EAAQS,MAAM,GAAI,IAG5BT,EAAUA,EAAQS,MAAM,GAAI,GAC5Ba,GAAgB,GAEb,IAAIT,OAAOb,GAA2B,IAAlBsB,EAA0B,SAAML,GAAWuE,KAAKrD,EAC9E,CACD,OAAOA,EAAKsD,SAASzF,EACzB,CA+BmB0F,CAAalE,EAAUW,EACjC,CACI,GAAsB,oBAAlBQ,EAASzB,KAA4B,CAC1C,MAAMyE,EAAYC,OAAOjD,EAASnB,UAClC,GAAIoE,OAAOC,MAAMF,IAAcA,EAAY,EACvC,OAAO,EAEX,MAAMxD,EAAO+C,EAAQG,YACrB,OAAa,OAATlD,GAGGA,EAAKxB,QAAUgF,CACzB,CACJ,CACD,OAAO,CACX,CACO,SAASP,EAAiBF,EAASvC,GACtC,MAAMmD,EAAW,GACjB,GAAsB,OAAlBnD,EAAS3D,MACS,UAAlB2D,EAAS3D,MACS,SAAlB2D,EAAS3D,MACS,cAAlB2D,EAAS3D,KACT8G,EAAS9C,QAAQkC,EAAQE,iBAAiBzC,EAASpC,eAElD,GAAsB,SAAlBoC,EAAS3D,KACd,IAAK,MAAM+G,KAAepD,EAASsB,KAC/B6B,EAAS9C,QAAQoC,EAAiBF,EAASa,SAG9C,GAAsB,aAAlBpD,EAAS3D,KAKmB,IAA7B2D,EAAS+B,SAAS/D,QAClBmF,EAAS9C,QAAQoC,EAAiBF,EAASvC,EAAS+B,SAAS,IAAIb,QAAQmC,GAAMrD,EAAS+B,SAASjE,MAAM,GAAG5B,OAAOsG,GAAMF,EAAQe,EAAGb,aAGrI,GAAsB,YAAlBxC,EAAS3D,KAAoB,CAClC,MAAMiH,OAA8BhF,IAAlB0B,EAAS6B,KAAqB,CAACU,GAAWE,EAAiBF,EAASvC,EAAS6B,MAC/F,GAA4B,MAAxB7B,EAASnD,WACT,IAAK,MAAM0G,KAAYD,EACnBH,EAAS9C,QAAQoC,EAAiBc,EAAUvD,EAAS8B,aAGxD,GAA4B,MAAxB9B,EAASnD,WACd,IAAK,MAAM0G,KAAYD,EACnB,IAAK,MAAME,KAASD,EAASE,UACc,IAAnCnB,EAAQkB,EAAOxD,EAAS8B,QACxBqB,EAAS9C,KAAKmD,QAKzB,GAA4B,MAAxBxD,EAASnD,WACd,IAAK,MAAM0G,KAAYD,EAAW,CAC9B,IAAII,EAAUH,EACd,KAAkD,QAA1CG,EAAUA,EAAQC,sBACmB,IAArCrB,EAAQoB,EAAS1D,EAAS8B,QAC1BqB,EAAS9C,KAAKqD,EAGzB,MAEA,GAA4B,MAAxB1D,EAASnD,WACd,IAAK,MAAM0G,KAAYD,EAAW,CAC9B,MAAMK,EAAqBJ,EAASI,mBACT,OAAvBA,IAA+E,IAAhDrB,EAAQqB,EAAoB3D,EAAS8B,QACpEqB,EAAS9C,KAAKsD,EAErB,CAER,MACI,GAAsB,iBAAlB3D,EAAS3D,KAyBd,IAAK,MAAMuH,KAAcrB,EAAQE,iBAAiB,MACR,IAAlCH,EAAQsB,EAAY5D,IACpBmD,EAAS9C,KAAKuD,GAK1B,OAAOT,CACX,CCvJY,MAACU,EAA0B,IAAItH,IAAI,CAI3C,MACA,WACA,OAYSuH,EAAiB,IAAIvH,IAAI,CAClC,SACA,MACA,WACA,QACA,UACA,UACA,UACA,MACA,WACA,QACA,UACA,QACA,cACA,gBACA,QACA,gBACA,eACA,aACA,OACA,eACA,QACA,WACA,gBACA,UACA,KACA,OACA,aACA,eACA,OACA,OACA,UAIA,MACA,YACA,iBACA,mBACA,cACA,aACA,eACA,WACA,eACA,oBACA,YACA,aACA,WACA,QACA,OACA,QACA,SACA,QACA,UACA,UAMSwH,EAAkB,IAAIxH,IAAI,CAAC,QAAS,SAAU,eAAgB,eAE3E,IAAWyH,EADaC,EAAAD,kBAAA,GACbA,EAIRA,iBAAiBA,EAAAA,aAAe,CAAE,IAHpBA,EAAqB,OAAI,GAAK,SAC3CA,EAAaA,EAAuB,SAAI,GAAK,WAC7CA,EAAaA,EAAsB,QAAI,GAAK,gGAEzC,SAASE,EAAiBlE,GAE7B,IAA+B,IAA3BA,EAASL,QAAQ,KACjB,OAAOqE,EAAAA,aAAaG,OAExB,MAAMlI,EAASsE,EAASP,GAExB,IAAIoE,GAAiC,EACrC,IAAK,MAAMjI,KAASF,EAChB,GAAmB,iBAAfE,EAAME,KAAyB,CAC/B,MAAMkC,KAAEA,GAASpC,EACjB,IAA0C,IAAtC0H,EAAwB1E,IAAIZ,GAC5B6F,GAAiC,OAEhC,IAAiC,IAA7BN,EAAe3E,IAAIZ,KAAiD,IAA9BwF,EAAgB5E,IAAIZ,GAC/D,OAAOyF,EAAAA,aAAaK,QAGxB,IAAuC,IAAnCD,QACmB9F,IAAnBnC,EAAM0C,WACiC,IAAvCvC,EAAyB6C,IAAIZ,GAAgB,CAC7C,MAAM+F,EAAeJ,EAAiB/H,EAAM0C,UAC5C,GAAIyF,IAAiBN,EAAYA,aAACK,QAC9B,OAAOC,EAEFA,IAAiBN,EAAYA,aAACO,WACnCH,GAAiC,EAExC,CACJ,CAEL,OAAuC,IAAnCA,EACOJ,EAAAA,aAAaO,SAEjBP,EAAAA,aAAaG,MACxB,4CFyVO,SAASK,EAAMxE,GAAUyE,UAAEA,GAAY,EAAInD,KAAEA,GAAO,GAAS,IAChE,MAAMrF,EAASsE,EAASP,GACxB,GAAsB,IAAlB/D,EAAO+B,OACP,OAEJ,MAAM0G,EAAMrD,EAAWpF,EAAQ,CAAEqF,SAWjC,OAVkB,IAAdmD,GACAzC,EAAK0C,GAAMzC,IACW,iBAAdA,EAAK5F,MACL4F,EAAKpD,eACSP,IAAd2D,EAAK1D,MACLjC,EAAyB6C,IAAI8C,EAAK1D,QAClC0D,EAAKnD,QAAU0F,EAAMvC,EAAKpD,SAAU,CAAE4F,WAAW,EAAMnD,MAAM,IAChE,IAGFoD,CACX"}