{"version": 3, "file": "events.js", "sourceRoot": "", "sources": ["../../src/events.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,6DAAsD;AActD;;GAEG;AACH,SAAS,gBAAgB,CACvB,KAAiB,EACjB,QAAuB,EACvB,SAAqC;IAErC,IAAI,iBAAiB,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAE7C,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QACpC,iBAAiB,GAAG,EAAE,CAAC;QACvB,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACzB,KAAiB,EACjB,QAAuB,EACvB,SAAqC;IAErC,MAAM,iBAAiB,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/C,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QACpC,MAAM,eAAe,GAAG,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3B,iBAAiB,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,eAAe,CACtB,KAAiB,EACjB,IAAW,EACX,SAAqC;IAErC,sDAAsD;IACtD,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,iBAAiB,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/C,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QACpC,IAAA,mCAAc,EAAC,GAAG,EAAE;YAClB,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;gBACzC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,MAAa,YAAY;IAAzB;QAIU,kBAAa,GAA+B,IAAI,GAAG,EAAE,CAAC;QACtD,gBAAW,GAA+B,IAAI,GAAG,EAAE,CAAC;IA8C9D,CAAC;IA5CC;;OAEG;IACI,EAAE,CACP,KAAgB,EAChB,QAAkC;QAElC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;;OAGG;IACI,IAAI,CACT,KAAgB,EAChB,QAAkC;QAElC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,WAAW,CAChB,KAAgB,EAChB,QAAkC;QAElC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,kBAAkB,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,IAAI,CACT,KAAgB,EAChB,GAAG,IAA0C;QAE7C,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;YAC9D,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;CACF;AAnDD,oCAmDC"}