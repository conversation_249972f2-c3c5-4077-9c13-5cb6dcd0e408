{"version": 3, "file": "html-filtering.js", "sourceRoot": "", "sources": ["../../src/html-filtering.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAYH,kEA0CC;AAED,kDA4EC;AAID,8DA4BC;AA2BD,gDAgBC;AAED,gDAYC;AAjND,SAAgB,2BAA2B,CAAC,IAAY;IACtD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,KAAK,EAAE,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,MAAM,GAAG,YAAY,CAAC;IAC5B,MAAM,SAAS,GAAa,EAAE,CAAC;IAE/B,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,mBAAmB;IACnB,aAAa;IAEb,kEAAkE;IAClE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC;QACtC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC;QACvB,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,MAAM,oBAAoB,GAAG,KAAK,CAAC;QACnC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB;QACpC,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,mBAAmB,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,IAAI,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;gBAC1B,IAAI,IAAI,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;oBAC1B,mBAAmB,IAAI,CAAC,CAAC;gBAC3B,CAAC;gBAED,IAAI,IAAI,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;oBAC1B,mBAAmB,IAAI,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,IAAI,GAAG,IAAI,CAAC;QACd,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC/B,CAAC;AAED,SAAgB,mBAAmB,CACjC,IAAY,EACZ,GAAW;IAEX,MAAM,IAAI,GAAuB,EAAE,CAAC;IACpC,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;IACzB,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC;IAE3B,wDAAwD;IACxD,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACjC,gEAAgE;IAChE,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;QACpB,sEAAsE;QACtE,sEAAsE;QACtE,cAAc;QACd,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/D,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;YACxD,YAAY,GAAG,aAAa,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACN,sEAAsE;YACtE,wEAAwE;YACxE,4BAA4B;YAC5B,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACzE,YAAY,GAAG,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC;QACnD,CAAC;QAED,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC7C,CAAC;IAED,6EAA6E;IAC7E,4EAA4E;IAC5E,YAAY;IACZ,EAAE;IACF,uEAAuE;IACvE,2EAA2E;IAC3E,2EAA2E;IAC3E,yEAAyE;IACzE,wEAAwE;IACxE,+CAA+C;IAC/C,IAAI,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAChD,IAAI,mBAAmB,KAAK,CAAC,CAAC,EAAE,CAAC;QAC/B,mBAAmB,GAAG,YAAY,CAAC;IACrC,CAAC;IAED,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;IAC9D,6EAA6E;IAC7E,wEAAwE;IACxE,gDAAgD;IAChD,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,2EAA2E;IAC3E,4EAA4E;IAC5E,eAAe;IACf,IACE,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,MAAM,CAAC,MAAM;QAC7C,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,KAAK,KAAK,EACvD,CAAC;QACD,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;AAC3E,CAAC;AAID,SAAgB,yBAAyB,CAAC,MAAsB;IAC9D,MAAM,QAAQ,GAA2B,EAAE,CAAC;IAE5C,KAAK,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,MAAM,EAAE,CAAC;QACvC,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,SAAS;QACX,CAAC;QACD,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,MAAM,cAAc,GAAa,EAAE,CAAC;QAEpC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;gBAC5C,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC3B,cAAc,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,CAAC;qBAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnC,cAAc,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,SAAS,kBAAkB,CACzB,GAAW,EACX,aAAgC,EAChC,cAAiC;IAEjC,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;QACpC,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE,CAAC;QACrC,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,kBAAkB,CAChC,QAAkB,EAClB,IAAwB;IAExB,MAAM,QAAQ,GAAuB,EAAE,CAAC;IAExC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,IAAI,QAAQ,EAAE,CAAC;YACvD,IAAI,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,cAAc,CAAC,EAAE,CAAC;gBAC9D,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnB,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAgB,kBAAkB,CAAC,IAAY,EAAE,QAA4B;IAC3E,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,gEAAgE;IACpF,KAAK,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,QAAQ,EAAE,CAAC;QACpC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;IACvF,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAY,EAAE,SAAyB;IACnE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,SAAS,EAAE,CAAC;QAC9C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAqB,mBAAmB;IAKtC,YAAY,SAAyB;QACnC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC9B,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;iBAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACpC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,qBAA8B,IAAI;QAC7C,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QAEtB,IAAI,kBAAkB,KAAK,IAAI,EAAE,CAAC;YAChC,wBAAwB;YACxB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,6CAA6C;gBAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/B,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;oBACxE,GAAG,GAAG,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;gBACnF,CAAC;gBAED,GAAG,GAAG,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,KAAK,CAAC,KAAa;QACxB,mCAAmC;QACnC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,qCAAqC;QACrC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC;YAErB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,+BAA+B;QAC/B,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC;QAErB,gCAAgC;QAChC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAExE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,wDAAwD;QACxD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,mEAAmE;QACnE,OAAO,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC;CACF;AA3ED,sCA2EC"}