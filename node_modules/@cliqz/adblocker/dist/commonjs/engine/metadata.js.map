{"version": 3, "file": "metadata.js", "sourceRoot": "", "sources": ["../../../src/engine/metadata.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;AAEH,qCAAsC;AAEtC,uEAAkD;AAElD,4DAMkC;AAElC,kEAMqC;AAErC,wDAKgC;AAQhC,wIAAwI;AAExI,yBAAyB;AACzB,yBAAyB;AACzB,wFAAwF;AACxF,6HAA6H;AAC7H,yIAAyI;AACzI,mKAAmK;AAEnK,MAAa,QAAQ;IACZ,MAAM,CAAC,WAAW,CAAC,MAAsB;QAC9C,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,QAAQ,CAAC,UAAU,GAAG,mBAAU,CAAC,WAAW,CAAC,MAAM,EAAE,2BAAmB,CAAC,CAAC;QAC1E,QAAQ,CAAC,aAAa,GAAG,mBAAU,CAAC,WAAW,CAAC,MAAM,EAAE,8BAAuB,CAAC,CAAC;QACjF,QAAQ,CAAC,QAAQ,GAAG,mBAAU,CAAC,WAAW,CAAC,MAAM,EAAE,yBAAkB,CAAC,CAAC;QACvE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAMD,YAAY,YAAiB;QAC3B,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG,IAAA,4BAAsB,EAAC,EAAE,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,GAAG,IAAA,yBAAmB,EAAC,EAAE,CAAC,CAAC;YAC1C,IAAI,CAAC,QAAQ,GAAG,IAAA,uBAAiB,EAAC,EAAE,CAAC,CAAC;YACtC,OAAO;QACT,CAAC;QAED,MAAM,EACJ,QAAQ,EAAE,WAAW,EACrB,aAAa,EAAE,gBAAgB,EAC/B,UAAU,EAAE,aAAa,GAC1B,GAAG,YAAY,CAAC;QAEjB,wBAAwB;QACxB,MAAM,UAAU,GAAgB,EAAE,CAAC;QACnC,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACtC,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5D,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBACjC,SAAS;gBACX,CAAC;gBACD,MAAM,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;gBAC7C,IAAI,IAAA,uBAAe,EAAC,eAAe,CAAC,EAAE,CAAC;oBACrC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,IAAA,yBAAmB,EAAC,UAAU,CAAC,CAAC;QAElD,2BAA2B;QAC3B,MAAM,aAAa,GAAoB,EAAE,CAAC;QAC1C,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YACzC,KAAK,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACnE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;oBACrC,SAAS;gBACX,CAAC;gBACD,MAAM,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;gBACrD,IAAI,IAAA,0BAAmB,EAAC,mBAAmB,CAAC,EAAE,CAAC;oBAC7C,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,mBAAmB,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAA,4BAAsB,EAAC,aAAa,CAAC,CAAC;QAE3D,sBAAsB;QACtB,MAAM,QAAQ,GAAe,EAAE,CAAC;QAChC,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;YACpC,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBACzD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;oBAChC,SAAS;gBACX,CAAC;gBACD,MAAM,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;gBAC3C,IAAI,IAAA,qBAAc,EAAC,cAAc,CAAC,EAAE,CAAC;oBACnC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAA,uBAAiB,EAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;IACxC,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO,CACL,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE;YACnC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAsB;QACrC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,MAAqB;QACrC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,MAAc;QAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEtC,OAAO,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,kBAAkB,GAAG,oBAAa,CAAC,KAAK,CAAC,KAAK,SAAS,GAAG,CAAC,CAAC;YAElE,IAAI,kBAAkB,KAAK,IAAI,EAAE,CAAC;gBAChC,SAAS;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC;YACzD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,EAAU;;QACtB,MAAM,OAAO,GAA2B,EAAE,CAAC;QAE3C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC;gBACX,OAAO;gBACP,QAAQ,EAAE,MAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAA,sBAAc,EAAC,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,0CAAG,CAAC,CAAC;gBAC7E,YAAY,EACV,OAAO,CAAC,YAAY,KAAK,IAAI;oBAC3B,CAAC,CAAC,MAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAA,yBAAkB,EAAC,EAAE,GAAG,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,0CAAG,CAAC,CAAC;oBAChF,CAAC,CAAC,IAAI;aACX,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAlKD,4BAkKC"}