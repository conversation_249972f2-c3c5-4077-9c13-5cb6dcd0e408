{"version": 3, "file": "categories.js", "sourceRoot": "", "sources": ["../../../../src/engine/metadata/categories.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAaH,0BA4BC;AAED,wBAEC;AAED,8CAOC;AAED,8BAKC;AAED,kCAOC;AAED,8BAQC;AA9ED,sCAAuC;AACvC,qDAAgE;AAChE,6CAA0C;AAS1C,SAAgB,OAAO,CAAC,QAAa;IACnC,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,QAAQ,CAAC;IAEnD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,MAAM,CAAC,QAAyB;IAC9C,OAAO,IAAA,mBAAQ,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,SAAgB,iBAAiB,CAAC,QAAmB;IACnD,OAAO,CACL,IAAA,yBAAU,EAAC,QAAQ,CAAC,GAAG,CAAC;QACxB,IAAA,yBAAU,EAAC,QAAQ,CAAC,IAAI,CAAC;QACzB,IAAA,yBAAU,EAAC,QAAQ,CAAC,KAAK,CAAC;QAC1B,IAAA,yBAAU,EAAC,QAAQ,CAAC,WAAW,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,SAAgB,SAAS,CAAC,QAAmB,EAAE,IAAoB;IACjE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACtC,CAAC;AAED,SAAgB,WAAW,CAAC,IAAoB;IAC9C,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;QACnB,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;QACpB,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE;QACrB,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE;KAC5B,CAAC;AACJ,CAAC;AAED,SAAgB,SAAS,CAAC,UAAuB;IAC/C,OAAO,IAAI,mBAAU,CAAC;QACpB,iBAAiB;QACjB,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,SAAS;QACT,WAAW;QACX,MAAM,EAAE,UAAU;KACnB,CAAC,CAAC;AACL,CAAC"}