"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const network_js_1 = __importDefault(require("../../filters/network.js"));
const cosmetic_js_1 = __importDefault(require("../../filters/cosmetic.js"));
const optimizer_js_1 = require("../optimizer.js");
const reverse_index_js_1 = __importDefault(require("../reverse-index.js"));
const cosmetic_js_2 = require("./cosmetic.js");
class HTMLBucket {
    static deserialize(buffer, config) {
        const bucket = new HTMLBucket({ config });
        bucket.networkIndex = reverse_index_js_1.default.deserialize(buffer, network_js_1.default.deserialize, config.enableOptimizations ? optimizer_js_1.optimizeNetwork : optimizer_js_1.noopOptimizeNetwork, config);
        bucket.exceptionsIndex = reverse_index_js_1.default.deserialize(buffer, network_js_1.default.deserialize, config.enableOptimizations ? optimizer_js_1.optimizeNetwork : optimizer_js_1.noopOptimizeNetwork, config);
        bucket.cosmeticIndex = reverse_index_js_1.default.deserialize(buffer, cosmetic_js_1.default.deserialize, optimizer_js_1.noopOptimizeCosmetic, config);
        bucket.unhideIndex = reverse_index_js_1.default.deserialize(buffer, cosmetic_js_1.default.deserialize, optimizer_js_1.noopOptimizeCosmetic, config);
        return bucket;
    }
    constructor({ filters = [], config, }) {
        this.config = config;
        this.networkIndex = new reverse_index_js_1.default({
            config,
            deserialize: network_js_1.default.deserialize,
            filters: [],
            optimize: config.enableOptimizations ? optimizer_js_1.optimizeNetwork : optimizer_js_1.noopOptimizeNetwork,
        });
        this.exceptionsIndex = new reverse_index_js_1.default({
            config,
            deserialize: network_js_1.default.deserialize,
            filters: [],
            optimize: config.enableOptimizations ? optimizer_js_1.optimizeNetwork : optimizer_js_1.noopOptimizeNetwork,
        });
        this.cosmeticIndex = new reverse_index_js_1.default({
            config,
            deserialize: cosmetic_js_1.default.deserialize,
            filters: [],
            optimize: optimizer_js_1.noopOptimizeCosmetic,
        });
        this.unhideIndex = new reverse_index_js_1.default({
            config,
            deserialize: cosmetic_js_1.default.deserialize,
            filters: [],
            optimize: optimizer_js_1.noopOptimizeCosmetic,
        });
        if (filters.length !== 0) {
            this.update(filters, undefined);
        }
    }
    update(newFilters, removedFilters) {
        const networkFilters = [];
        const exceptionFilters = [];
        const cosmeticFilters = [];
        const unhideFilters = [];
        for (const filter of newFilters) {
            if (filter.isNetworkFilter()) {
                if (filter.isException()) {
                    exceptionFilters.push(filter);
                }
                else {
                    networkFilters.push(filter);
                }
            }
            else if (filter.isCosmeticFilter()) {
                if (filter.isUnhide()) {
                    unhideFilters.push(filter);
                }
                else {
                    cosmeticFilters.push(filter);
                }
            }
        }
        this.networkIndex.update(networkFilters, removedFilters);
        this.exceptionsIndex.update(exceptionFilters, removedFilters);
        this.cosmeticIndex.update(cosmeticFilters, removedFilters);
        this.unhideIndex.update(unhideFilters, removedFilters);
    }
    serialize(buffer) {
        this.networkIndex.serialize(buffer);
        this.exceptionsIndex.serialize(buffer);
        this.cosmeticIndex.serialize(buffer);
        this.unhideIndex.serialize(buffer);
    }
    getSerializedSize() {
        return (this.networkIndex.getSerializedSize() +
            this.exceptionsIndex.getSerializedSize() +
            this.cosmeticIndex.getSerializedSize() +
            this.unhideIndex.getSerializedSize());
    }
    getHTMLFilters(request, isFilterExcluded) {
        const networkFilters = [];
        const cosmeticFilters = [];
        const exceptions = [];
        const unhides = [];
        if (this.config.loadNetworkFilters === true) {
            this.networkIndex.iterMatchingFilters(request.getTokens(), (filter) => {
                if (filter.match(request) && !(isFilterExcluded === null || isFilterExcluded === void 0 ? void 0 : isFilterExcluded(filter))) {
                    networkFilters.push(filter);
                }
                return true;
            });
        }
        // If we found at least one candidate, check if we have exceptions.
        if (networkFilters.length !== 0) {
            this.exceptionsIndex.iterMatchingFilters(request.getTokens(), (filter) => {
                if (filter.match(request) && !(isFilterExcluded === null || isFilterExcluded === void 0 ? void 0 : isFilterExcluded(filter))) {
                    exceptions.push(filter);
                }
                return true;
            });
        }
        if (this.config.loadCosmeticFilters === true && request.isMainFrame()) {
            const { hostname, domain = '' } = request;
            const hostnameTokens = (0, cosmetic_js_2.createLookupTokens)(hostname, domain);
            this.cosmeticIndex.iterMatchingFilters(hostnameTokens, (filter) => {
                if (filter.match(hostname, domain) && !(isFilterExcluded === null || isFilterExcluded === void 0 ? void 0 : isFilterExcluded(filter))) {
                    cosmeticFilters.push(filter);
                }
                return true;
            });
            // If we found at least one candidate, check if we have unhidden rules.
            if (cosmeticFilters.length !== 0) {
                this.unhideIndex.iterMatchingFilters(hostnameTokens, (rule) => {
                    if (rule.match(hostname, domain) && !(isFilterExcluded === null || isFilterExcluded === void 0 ? void 0 : isFilterExcluded(rule))) {
                        unhides.push(rule);
                    }
                    return true;
                });
            }
        }
        return {
            networkFilters,
            cosmeticFilters,
            unhides,
            exceptions,
        };
    }
    getFilters() {
        const filters = [];
        return filters.concat(this.networkIndex.getFilters(), this.exceptionsIndex.getFilters(), this.cosmeticIndex.getFilters(), this.unhideIndex.getFilters());
    }
}
exports.default = HTMLBucket;
//# sourceMappingURL=html.js.map