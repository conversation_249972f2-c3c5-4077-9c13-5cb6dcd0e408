"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
/* eslint-disable prettier/prettier */
exports.default = [
    ")%7D)()%3Bdocument.currentScript.textContent%3Ddocument.currentScript.textContent.replace(%2F%5C%2F%5C*start%5C*%5C%2F(.*)%5C%2F%5",
    "%3D%3D%2527%252Cnecessary%3Atrue%252Cpreferences%3Afalse%252Cstatistics%3Afalse%252Cmarketing%3Afalse%252Cmethod%3A%2527explicit%2",
    "div[style=\"position: absolute; inset: 0px; overflow: hidden; z-index: 160; background: transparent none repeat scroll 0% 0%; displ",
    "div[style=\"position: fixed; display: block; width: 100%; height: 100%; inset: 0px; background-color: rgba(0, 0, 0, 0); z-index: 30",
    "trusted-set-cookie, SOCS, CAISNQgQEitib3FfaWRlbnRpdHlmcm9udGVuZHVpc2VydmVyXzIwMjQwNTE0LjA2X3AwGgJmaSADGgYIgOu0sgY, 1year, , domain",
    ", %2F*start*%2F(function()%7Blet%20link%3Ddocument.createElement(%22link%22)%3Blink.rel%3D%22stylesheet%22%3Blink.href%3D%22%2F",
    "acs, atob, %2FpopundersPerIP%5B%5Cs%5CS%5D*%3FDate%5B%5Cs%5CS%5D*%3FgetElementsByTagName%5B%5Cs%5CS%5D*%3FinsertBefore%2F",
    ".entry-content > figure.wp-block-image:has(> img[class^=\"wp-image-\"][src^=\"https://www.sinhasannews.com/\"][width=\"",
    "\"]:not([style^=\"width: 1px; height: 1px; position: absolute; left: -10000px; top: -\"])",
    "acs, document.createElement, %2Fl%5C.parentNode%5C.insertBefore%5C(s%2F",
    "%2Fvisit%2F%22%5D%5Btitle%5E%3D%22https%3A%2F%2F%22%5D, %5Btitle%5D",
    ", OptanonConsent, groups%3DC0001%253A1%252CC0002%253A0%252CC000",
    "rmnt, script, %2Fh%3DdecodeURIComponent%7CpopundersPerIP%2F",
    ":not([style^=\"position: absolute; left: -5000px\"])",
    "href-sanitizer, a%5Bhref%5E%3D%22https%3A%2F%2F",
    "ra, oncontextmenu%7Condragstart%7Conselectstart",
    ", OptanonAlertBoxClosed, %24currentDate%24",
    "acs, document.querySelectorAll, popMagic",
    "acs, addEventListener, google_ad_client",
    "aost, String.prototype.charCodeAt, ai_",
    "aopr, app_vars.force_disable_adblock",
    "acs, document.addEventListener, ",
    "acs, document.getElementById, ",
    "no-fetch-if, googlesyndication",
    "aopr, document.dispatchEvent",
    "no-xhr-if, googlesyndication",
    ", document.createElement, ",
    "acs, String.fromCharCode, ",
    "%2522%253Afalse%252C%2522",
    ", document.oncontextmenu",
    "%2522%253Atrue%252C%2522",
    "aeld, DOMContentLoaded, ",
    "nosiif, visibility, 1000",
    "set-local-storage-item, ",
    "%2522%3Afalse%252C%2522",
    "trusted-click-element, ",
    "set, blurred, false",
    "acs, eval, replace",
    "[target=\"_blank\"]",
    "%22%3Afalse%2C%22",
    "^script:has-text(",
    "[href^=\"https://",
    "[href^=\"http://",
    "[href=\"https://",
    "[src^=\"https://",
    "[data-testid=\"",
    "modal-backdrop",
    "rmnt, script, ",
    "BlockDetected",
    "trusted-set-",
    ".prototype.",
    "contextmenu",
    "no-fetch-if",
    "otification",
    ":has-text(",
    "background",
    "[class*=\"",
    "[class^=\"",
    "body,html",
    "container",
    "Container",
    "decodeURI",
    "div[class",
    "div[id^=\"",
    "div[style",
    "document.",
    "no-xhr-if",
    "placehold",
    "[href*=\"",
    "#wpsafe-",
    "AAAAAAAA",
    "Detector",
    "disclaim",
    "nano-sib",
    "nextFunc",
    "noopFunc",
    "nostif, ",
    "nowebrtc",
    ".com/\"]",
    "300x250",
    "article",
    "consent",
    "Consent",
    "content",
    "display",
    "keydown",
    "message",
    "Message",
    "overlay",
    "privacy",
    "sidebar",
    "sponsor",
    "wrapper",
    "-child",
    "[data-",
    "accept",
    "Accept",
    "aopr, ",
    "banner",
    "bottom",
    "cookie",
    "Cookie",
    "google",
    "nosiif",
    "notice",
    "nowoif",
    "policy",
    "Policy",
    "script",
    "widget",
    ":has(",
    ":not(",
    "block",
    "Block",
    "click",
    "deskt",
    "disab",
    "fixed",
    "frame",
    "modal",
    "popup",
    "video",
    ".com",
    "2%3A",
    "aeld",
    "body",
    "butt",
    "foot",
    "gdpr",
    "html",
    "icky",
    "ight",
    "show",
    "tion",
    "true",
    " > ",
    "%3D",
    "%7C",
    "age",
    "box",
    "div",
    "ent",
    "out",
    "rap",
    "set",
    "__",
    ", ",
    "\"]",
    "%2",
    "%5",
    "=\"",
    "00",
    "ac",
    "ad",
    "Ad",
    "al",
    "an",
    "ar",
    "at",
    "e-",
    "ed",
    "en",
    "er",
    "he",
    "id",
    "in",
    "la",
    "le",
    "lo",
    "od",
    "ol",
    "om",
    "on",
    "op",
    "or",
    "re",
    "s_",
    "s-",
    "se",
    "st",
    "t-",
    "te",
    "ti",
    "un",
    " ",
    "_",
    "-",
    ";",
    ":",
    ".",
    "(",
    ")",
    "[",
    "]",
    "*",
    "/",
    "#",
    "^",
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "b",
    "B",
    "c",
    "C",
    "d",
    "D",
    "e",
    "E",
    "f",
    "F",
    "g",
    "G",
    "h",
    "H",
    "I",
    "j",
    "J",
    "k",
    "l",
    "L",
    "m",
    "M",
    "n",
    "N",
    "o",
    "O",
    "p",
    "P",
    "q",
    "Q",
    "r",
    "R",
    "s",
    "S",
    "t",
    "T",
    "u",
    "U",
    "v",
    "V",
    "w",
    "W",
    "x",
    "y",
    "Y",
    "z"
];
//# sourceMappingURL=cosmetic-selector.js.map