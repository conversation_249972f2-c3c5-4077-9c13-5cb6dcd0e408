"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
/* eslint-disable prettier/prettier */
exports.default = [
    "google-analytics.com/analytics.js",
    "googlesyndication_adsbygoogle.js",
    "googletagmanager.com/gtm.js",
    "googletagservices_gpt.js",
    "googletagmanager_gtm.js",
    "fuckadblock.js-3.2.0",
    "amazon_apstag.js",
    "google-analytics",
    "fingerprint2.js",
    "noop-1s.mp4:10",
    "google-ima.js",
    "noop-0.1s.mp3",
    "prebid-ads.js",
    "nobab2.js:10",
    "noopmp3-0.1s",
    "noop-1s.mp4",
    "hd-main.js",
    "noopmp4-1s",
    "32x32.png",
    "noop.html",
    "noopframe",
    "noop.txt",
    "nooptext",
    "1x1.gif",
    "2x2.png",
    "noop.js",
    "noopjs",
    ".com/",
    ".js:5",
    "noop",
    ":10",
    ".js",
    "ads",
    "bea",
    "_a",
    ":5",
    ".0",
    "ar",
    "ch",
    "ic",
    "in",
    "le",
    "ma",
    "on",
    "re",
    "st",
    "_",
    "-",
    ":",
    ".",
    "/",
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z"
];
//# sourceMappingURL=network-redirect.js.map