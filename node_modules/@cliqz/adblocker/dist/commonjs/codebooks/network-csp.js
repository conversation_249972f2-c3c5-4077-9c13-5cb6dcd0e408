"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
/* eslint-disable prettier/prettier */
exports.default = [
    "sandbox allow-forms allow-same-origin allow-scripts allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' ",
    " *.google.com *.gstatic.com *.googleapis.com",
    ".com *.google.com *.googletagmanager.com *.",
    "script-src 'self' '*' 'unsafe-inline'",
    "default-src 'unsafe-inline' 'self'",
    "script-src 'self' 'unsafe-eval' ",
    " *.google.com *.gstatic.com *.",
    "t-src 'self' 'unsafe-inline' ",
    "script-src * 'unsafe-inline'",
    ".com *.googleapis.com *.",
    " *.googletagmanager.com",
    ".com *.bootstrapcdn.com",
    "default-src 'self' *.",
    "frame-src 'self' *",
    " *.cloudflare.com",
    "child-src 'none';",
    "worker-src 'none'",
    "'unsafe-inline'",
    " 'unsafe-eval'",
    "*.googleapis",
    "connect-src ",
    "child-src *",
    " *.gstatic",
    "script-src",
    "style-src ",
    "frame-src",
    "facebook",
    "https://",
    " 'self'",
    " allow-",
    ".com *.",
    ".net *.",
    "addthis",
    "captcha",
    "gstatic",
    "youtube",
    " data:",
    "defaul",
    "disqus",
    "google",
    "https:",
    "jquery",
    "data:",
    "http:",
    "media",
    "scrip",
    "-src",
    ".com",
    ".net",
    "n.cc",
    " *.",
    "age",
    "box",
    "str",
    "vic",
    "yti",
    " *",
    ": ",
    "*.",
    "al",
    "am",
    "an",
    "cd",
    "el",
    "es",
    "il",
    "im",
    "in",
    "lo",
    "or",
    "pi",
    "st",
    "ur",
    "wi",
    "wp",
    " ",
    "-",
    ";",
    ":",
    ".",
    "'",
    "*",
    "/",
    "3",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y"
];
//# sourceMappingURL=network-csp.js.map