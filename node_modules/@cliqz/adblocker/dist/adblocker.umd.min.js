!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@remusao/smaz"),require("@cliqz/adblocker-extended-selectors"),require("@remusao/guess-url-type"),require("tldts-experimental"),require("@remusao/small")):"function"==typeof define&&define.amd?define(["exports","@remusao/smaz","@cliqz/adblocker-extended-selectors","@remusao/guess-url-type","tldts-experimental","@remusao/small"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).adblocker={},e.smaz,e.adblockerExtendedSelectors,e.guessUrlType,e.tldtsExperimental,e.small)}(this,(function(e,t,s,i,r,o){"use strict";var n=[")%7D)()%3Bdocument.currentScript.textContent%3Ddocument.currentScript.textContent.replace(%2F%5C%2F%5C*start%5C*%5C%2F(.*)%5C%2F%5","%3D%3D%2527%252Cnecessary%3Atrue%252Cpreferences%3Afalse%252Cstatistics%3Afalse%252Cmarketing%3Afalse%252Cmethod%3A%2527explicit%2",'div[style="position: absolute; inset: 0px; overflow: hidden; z-index: 160; background: transparent none repeat scroll 0% 0%; displ','div[style="position: fixed; display: block; width: 100%; height: 100%; inset: 0px; background-color: rgba(0, 0, 0, 0); z-index: 30',"trusted-set-cookie, SOCS, CAISNQgQEitib3FfaWRlbnRpdHlmcm9udGVuZHVpc2VydmVyXzIwMjQwNTE0LjA2X3AwGgJmaSADGgYIgOu0sgY, 1year, , domain",", %2F*start*%2F(function()%7Blet%20link%3Ddocument.createElement(%22link%22)%3Blink.rel%3D%22stylesheet%22%3Blink.href%3D%22%2F","acs, atob, %2FpopundersPerIP%5B%5Cs%5CS%5D*%3FDate%5B%5Cs%5CS%5D*%3FgetElementsByTagName%5B%5Cs%5CS%5D*%3FinsertBefore%2F",'.entry-content > figure.wp-block-image:has(> img[class^="wp-image-"][src^="https://www.sinhasannews.com/"][width="','"]:not([style^="width: 1px; height: 1px; position: absolute; left: -10000px; top: -"])',"acs, document.createElement, %2Fl%5C.parentNode%5C.insertBefore%5C(s%2F","%2Fvisit%2F%22%5D%5Btitle%5E%3D%22https%3A%2F%2F%22%5D, %5Btitle%5D",", OptanonConsent, groups%3DC0001%253A1%252CC0002%253A0%252CC000","rmnt, script, %2Fh%3DdecodeURIComponent%7CpopundersPerIP%2F",':not([style^="position: absolute; left: -5000px"])',"href-sanitizer, a%5Bhref%5E%3D%22https%3A%2F%2F","ra, oncontextmenu%7Condragstart%7Conselectstart",", OptanonAlertBoxClosed, %24currentDate%24","acs, document.querySelectorAll, popMagic","acs, addEventListener, google_ad_client","aost, String.prototype.charCodeAt, ai_","aopr, app_vars.force_disable_adblock","acs, document.addEventListener, ","acs, document.getElementById, ","no-fetch-if, googlesyndication","aopr, document.dispatchEvent","no-xhr-if, googlesyndication",", document.createElement, ","acs, String.fromCharCode, ","%2522%253Afalse%252C%2522",", document.oncontextmenu","%2522%253Atrue%252C%2522","aeld, DOMContentLoaded, ","nosiif, visibility, 1000","set-local-storage-item, ","%2522%3Afalse%252C%2522","trusted-click-element, ","set, blurred, false","acs, eval, replace",'[target="_blank"]',"%22%3Afalse%2C%22","^script:has-text(",'[href^="https://','[href^="http://','[href="https://','[src^="https://','[data-testid="',"modal-backdrop","rmnt, script, ","BlockDetected","trusted-set-",".prototype.","contextmenu","no-fetch-if","otification",":has-text(","background",'[class*="','[class^="',"body,html","container","Container","decodeURI","div[class",'div[id^="',"div[style","document.","no-xhr-if","placehold",'[href*="',"#wpsafe-","AAAAAAAA","Detector","disclaim","nano-sib","nextFunc","noopFunc","nostif, ","nowebrtc",'.com/"]',"300x250","article","consent","Consent","content","display","keydown","message","Message","overlay","privacy","sidebar","sponsor","wrapper","-child","[data-","accept","Accept","aopr, ","banner","bottom","cookie","Cookie","google","nosiif","notice","nowoif","policy","Policy","script","widget",":has(",":not(","block","Block","click","deskt","disab","fixed","frame","modal","popup","video",".com","2%3A","aeld","body","butt","foot","gdpr","html","icky","ight","show","tion","true"," > ","%3D","%7C","age","box","div","ent","out","rap","set","__",", ",'"]',"%2","%5",'="',"00","ac","ad","Ad","al","an","ar","at","e-","ed","en","er","he","id","in","la","le","lo","od","ol","om","on","op","or","re","s_","s-","se","st","t-","te","ti","un"," ","_","-",";",":",".","(",")","[","]","*","/","#","^","0","1","2","3","4","5","6","7","8","9","b","B","c","C","d","D","e","E","f","F","g","G","h","H","I","j","J","k","l","L","m","M","n","N","o","O","p","P","q","Q","r","R","s","S","t","T","u","U","v","V","w","W","x","y","Y","z"],a=["sandbox allow-forms allow-same-origin allow-scripts allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation","script-src 'self' 'unsafe-inline' 'unsafe-eval' "," *.google.com *.gstatic.com *.googleapis.com",".com *.google.com *.googletagmanager.com *.","script-src 'self' '*' 'unsafe-inline'","default-src 'unsafe-inline' 'self'","script-src 'self' 'unsafe-eval' "," *.google.com *.gstatic.com *.","t-src 'self' 'unsafe-inline' ","script-src * 'unsafe-inline'",".com *.googleapis.com *."," *.googletagmanager.com",".com *.bootstrapcdn.com","default-src 'self' *.","frame-src 'self' *"," *.cloudflare.com","child-src 'none';","worker-src 'none'","'unsafe-inline'"," 'unsafe-eval'","*.googleapis","connect-src ","child-src *"," *.gstatic","script-src","style-src ","frame-src","facebook","https://"," 'self'"," allow-",".com *.",".net *.","addthis","captcha","gstatic","youtube"," data:","defaul","disqus","google","https:","jquery","data:","http:","media","scrip","-src",".com",".net","n.cc"," *.","age","box","str","vic","yti"," *",": ","*.","al","am","an","cd","el","es","il","im","in","lo","or","pi","st","ur","wi","wp"," ","-",";",":",".","'","*","/","3","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y"],c=["/homad-global-configs.schneevonmorgen.com/global_config","/videojs-vast-vpaid@2.0.2/bin/videojs_5.vast.vpaid.min","/etc.clientlibs/logitech-common/clientlibs/onetrust.","/pagead/managed/js/adsense/*/show_ads_impl","/pagead/managed/js/gpt/*/pubads_impl","/wrappermessagingwithoutdetection","/pagead/js/adsbygoogle.js","a-z]{8,15}\\.(?:com|net)\\/","/js/sdkloader/ima3.js","/js/sdkloader/ima3_d","/videojs-contrib-ads","/wp-content/plugins/","/wp-content/uploads/","/wp-content/themes/","/detroitchicago/","*/satellitelib-","/appmeasurement","/413gkwmt/init","/cdn-cgi/trace","/^https?:\\/\\/","[a-zA-Z0-9]{","/^https:\\/\\/","notification","\\/[a-z0-9]{","fingerprint","impression","[0-9a-z]{","/plugins/","affiliate","analytics","telemetry","(.+?\\.)?","/assets/","/images/","/pagead/","pageview","template","tracking","/public","300x250","ampaign","collect","consent","content","counter","default","metrics","privacy","[a-z]{","/embed","728x90","banner","bundle","client","cookie","detect","dn-cgi","google","iframe","module","prebid","script","source","widget",".aspx",".cgi?",".com/",".html","/api/","/beac","/img/","/java","/stat","0x600","block","click","count","event","manag","media","pixel","popup","tegra","theme","track","type=","video","visit",".css",".gif",".jpg",".min",".php",".png","/jqu","/js/","/lib","/log","/web","/wp-","468x","data","gdpr","gi-b","http","ight","mail","play","plug","publ","show","stat","uild","view",".js","/ad","=*&","age","com","ext","id=","jax","key","log","new","sdk","tag","web","ync",":/","*/","*^","/_","/?","/*","/d","/f","/g","/h","/l","/n","/r","/u","/w","ac","ad","al","am","an","ap","ar","as","at","bo","ce","ch","co","de","di","e/","ec","ed","el","en","er","et","fi","g/","ic","id","im","in","it","js","la","le","li","lo","ma","mo","mp","ol","om","on","op","or","ot","re","ro","s_","s-","s?","s/","si","sp","st","t/","ti","tm","tr","ub","un","ur","us","ut","ve","_","-",",","?",".","}","*","/","\\","&","^","=","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],l=["securepubads.g.doubleclick",".actonservice.com","googlesyndication","imasdk.googleapis",".cloudfront.net","analytics.","marketing.","tracking.","metrics.","images.",".co.jp",".co.uk","a8clk.","stats.","a8cv.","track",".com",".net",".xyz","ight","tion","www.",".de",".io",".jp","app","cdn","new","web",".b",".c",".d",".f",".h",".j",".k",".l",".m",".n",".p",".s",".t",".v",".w","24","a-","a1","a2","a4","a8","ab","ac","ad","af","ag","ah","ai","ak","al","am","an","ap","ar","as","at","au","av","aw","ax","ay","az","be","bl","bo","br","bu","ca","ce","ch","ck","cl","cr","ct","cu","de","di","dn","do","dr","ds","e-","eb","ec","ed","ef","eg","el","em","en","ep","er","es","et","eu","ev","ew","ex","ey","fi","fl","fo","fr","ge","gh","gl","go","gr","gs","gu","he","ho","ia","ib","ic","id","ie","if","ig","ik","il","im","in","ip","ir","is","it","iv","ix","iz","ks","ld","le","li","lo","lu","ly","ma","me","mo","mp","my","nd","no","nt","ob","oc","od","of","ok","ol","om","on","oo","op","or","os","ot","ou","ov","ow","ph","pl","po","pr","pu","qu","ra","re","ro","ru","s-","sc","se","sh","si","sk","sl","sn","sp","ss","st","su","sw","sy","t-","ta","te","th","ti","tn","to","tr","ts","tu","ty","ub","ul","um","un","up","ur","us","ve","vi","we","wh","-",".","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],h=["google-analytics.com/analytics.js","googlesyndication_adsbygoogle.js","googletagmanager.com/gtm.js","googletagservices_gpt.js","googletagmanager_gtm.js","fuckadblock.js-3.2.0","amazon_apstag.js","google-analytics","fingerprint2.js","noop-1s.mp4:10","google-ima.js","noop-0.1s.mp3","prebid-ads.js","nobab2.js:10","noopmp3-0.1s","noop-1s.mp4","hd-main.js","noopmp4-1s","32x32.png","noop.html","noopframe","noop.txt","nooptext","1x1.gif","2x2.png","noop.js","noopjs",".com/",".js:5","noop",":10",".js","ads","bea","_a",":5",".0","ar","ch","ic","in","le","ma","on","re","st","_","-",":",".","/","0","1","2","3","4","5","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","r","s","t","u","v","w","x","y","z"],u=[",redirect=google-ima","/js/sdkloader/ima3.j","/wp-content/plugins/",",redirect-rule=noop",".com^$third-party","googlesyndication","imasdk.googleapis",".cloudfront.net^",",redirect-rule=","$script,domain=",",3p,denyallow=",",redirect=noop","xmlhttprequest",".actonservice","^$third-party","||smetrics.","third-party","marketing.","$document","analytics",",domain=","/assets/","metrics.","subdocum","tracking","$script",".co.uk","$ghide","a8clk.","cookie","google","script",".com^",".xyz^","$doma","a8cv.","image","media","track",".com",".fr^",".gif",".jp^",".net","/js/","$doc","$xhr","www.",",1p",",3p",".io",".jp",".js","app","cdn","ent","new","web",".b",".c",".d",".f",".h",".m",".n",".p",".s",".t","@@","/*","/p","||","a1","ab","ac","ad","af","ag","ai","ak","al","am","an","ap","ar","as","at","au","av","aw","ax","ay","az","be","bo","br","ca","ce","ch","ck","ct","cu","de","di","do","e-","e^","ec","ed","el","em","en","ep","er","es","et","ev","ew","ex","fe","ff","fi","fo","fr","g^","ge","gi","go","gr","he","hi","ho","hp","ht","ic","id","ig","il","im","in","io","ip","ir","is","it","ix","iz","js","ke","le","li","lo","lu","ly","me","mo","mp","my","ne","no","od","ok","ol","om","on","op","or","ot","pl","po","pr","qu","re","ri","ro","ru","s-","s/","sc","se","sh","si","so","sp","ss","st","su","te","th","ti","to","tr","ts","ty","ub","ud","ul","um","un","up","ur","us","ut","ve","vi","we","_","-",",","?",".","*","/","^","=","|","~","$","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],d=["-webkit-touch-callo",", 1year, , domain, ",", googlesyndication",", SOCS, CAISNQgQEit",":style(overflow: au","##^script:has-text(","9udGVuZHVpc2VydmVyX","GgJmaSADGgYIgOu0sgY","ib3FfaWRlbnRpdHlmcm","position: initial !","set-local-storage-i","set, blurred, false","user-select: text !","zIwMjQwNTE0LjA2X3Aw",'[href^="https://',"rmnt, script, ","ut: default !"," !important)","trusted-set-",", document.",", noopFunc)","##body,html","contextmenu","no-fetch-if","otification",".com##+js(",'="https://',"background","important;"," -webkit-",".*,xhamst","container","AAAAAAAA","nostif, ",",google",":style(","consent","message","nowoif)","privacy","-wrapp",",kayak",".co.uk","[class","##+js(","accept","aopr, ","banner","bottom","cookie","Cookie","google","notice","policy","widget",":has(","##div","block","cript","true)",".co.",".com",".de,",".fr,",".net",".nl,",".pl,",".xyz","#@#.","2%3A","gdpr","html","ight","news","text","to !","wrap","www."," > ",",xh","##.","###","%3D","%7C","ent","lay","web","__","-s",", ",",b",",c",",f",",g",",h",",m",",p",",s",",t",": ",".*",".b",".c",".m",".p",".s",'"]',"##","%2","%5",'="',"00","a-","ab","ac","ad","Ad","af","ag","ak","al","am","an","ap","ar","as","at","au","av","ay","az","bo","ch","ck","cl","ct","de","di","do","e-","ed","el","em","en","er","es","et","ex","fi","fo","he","ic","id","if","ig","il","im","in","is","it","iv","le","lo","mo","ol","om","on","op","or","ot","ov","pl","po","re","ro","s_","s-","se","sh","si","sp","st","t-","th","ti","tr","tv","ub","ul","um","un","up","ur","us","ut","vi"," ","_","-",",",":",".","(",")","[","*","/","^","0","1","2","3","4","5","6","7","8","9","a","b","B","c","C","d","D","e","E","f","F","g","h","i","j","k","l","L","m","M","n","o","p","P","q","r","s","S","t","T","u","v","w","x","y","z"];class f{constructor(){this.cosmeticSelector=new t.Smaz(n),this.networkCSP=new t.Smaz(a),this.networkRedirect=new t.Smaz(h),this.networkHostname=new t.Smaz(l),this.networkFilter=new t.Smaz(c),this.networkRaw=new t.Smaz(u),this.cosmeticRaw=new t.Smaz(d)}}const p=(()=>{let e=0;const t=new Int32Array(256);for(let s=0;256!==s;s+=1)e=s,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,t[s]=e;return t})();const g=2147483647,m=36,y=1,w=26,b=38,v=700,k=72,x=128,S="-",F=/[^\0-\x7E]/,A=/[\x2E\u3002\uFF0E\uFF61]/g,z={"invalid-input":"Invalid input","not-basic":"Illegal input >= 0x80 (not a basic code point)",overflow:"Overflow: input needs wider integers to process"},I=m-y;function C(e){throw new RangeError(z[e])}function R(e,t){return e+22+75*(e<26?1:0)-0}function U(e,t,s){let i=0;for(e=s?Math.floor(e/v):e>>1,e+=Math.floor(e/t);e>I*w>>1;i+=m)e=Math.floor(e/I);return Math.floor(i+(I+1)*e/(e+b))}function T(e){const t=[],s=e.length;let i=0,r=x,o=k,n=e.lastIndexOf(S);n<0&&(n=0);for(let s=0;s<n;++s)e.charCodeAt(s)>=128&&C("not-basic"),t.push(e.charCodeAt(s));for(let c=n>0?n+1:0;c<s;){const n=i;for(let t=1,r=m;;r+=m){c>=s&&C("invalid-input");const n=(a=e.charCodeAt(c++))-48<10?a-22:a-65<26?a-65:a-97<26?a-97:m;(n>=m||n>Math.floor((g-i)/t))&&C("overflow"),i+=n*t;const l=r<=o?y:r>=o+w?w:r-o;if(n<l)break;const h=m-l;t>Math.floor(g/h)&&C("overflow"),t*=h}const l=t.length+1;o=U(i-n,l,0===n),Math.floor(i/l)>g-r&&C("overflow"),r+=Math.floor(i/l),i%=l,t.splice(i++,0,r)}var a;return String.fromCodePoint.apply(null,t)}function E(e){const t=[],s=function(e){const t=[];let s=0;const i=e.length;for(;s<i;){const r=e.charCodeAt(s++);if(r>=55296&&r<=56319&&s<i){const i=e.charCodeAt(s++);56320==(64512&i)?t.push(((1023&r)<<10)+(1023&i)+65536):(t.push(r),s--)}else t.push(r)}return t}(e),i=s.length;let r=x,o=0,n=k;for(let e=0;e<s.length;e+=1){const i=s[e];i<128&&t.push(String.fromCharCode(i))}const a=t.length;let c=a;for(a&&t.push(S);c<i;){let e=g;for(let t=0;t<s.length;t+=1){const i=s[t];i>=r&&i<e&&(e=i)}const i=c+1;e-r>Math.floor((g-o)/i)&&C("overflow"),o+=(e-r)*i,r=e;for(let e=0;e<s.length;e+=1){const l=s[e];if(l<r&&++o>g&&C("overflow"),l===r){let e=o;for(let s=m;;s+=m){const i=s<=n?y:s>=n+w?w:s-n;if(e<i)break;const r=e-i,o=m-i;t.push(String.fromCharCode(R(i+r%o))),e=Math.floor(r/o)}t.push(String.fromCharCode(R(e))),n=U(o,i,c===a),o=0,++c}}++o,++r}return t.join("")}function O(e){const t=e.replace(A,".").split("."),s=[];for(let e=0;e<t.length;e+=1)s.push(F.test(t[e])?"xn--"+E(t[e]):t[e]);return s.join(".")}const H=new Uint8Array(0),j=new Uint32Array(0),P=1===new Int8Array(new Int16Array([1]).buffer)[0];let L,D=()=>{const e=new f;return D=()=>e,e};function B(e){return e<=127?1:5}function N(e,t){return M(e.length,t)}function M(e,t){return(t?3:0)+e+B(e)}function _(e){return e.length+B(e.length)}function $(e){const t=E(e).length;return t+B(t)}function q(e){return e.byteLength+B(e.length)}class V{static empty(e){return V.fromUint8Array(H,e)}static fromUint8Array(e,t){return new V(e,t)}static allocate(e,t){return new V(new Uint8Array(e),t)}constructor(e,{enableCompression:t}){if(!1===P)throw new Error("Adblocker currently does not support Big-endian systems");!0===t&&this.enableCompression(),this.buffer=e,this.pos=0}enableCompression(){this.compression=D()}checksum(){return function(e,t,s){let i=-1;const r=s-7;let o=t;for(;o<r;)i=i>>>8^p[255&(i^e[o++])],i=i>>>8^p[255&(i^e[o++])],i=i>>>8^p[255&(i^e[o++])],i=i>>>8^p[255&(i^e[o++])],i=i>>>8^p[255&(i^e[o++])],i=i>>>8^p[255&(i^e[o++])],i=i>>>8^p[255&(i^e[o++])],i=i>>>8^p[255&(i^e[o++])];for(;o<r+7;)i=i>>>8^p[255&(i^e[o++])];return~i>>>0}(this.buffer,0,this.pos)}dataAvailable(){return this.pos<this.buffer.byteLength}setPos(e){this.pos=e}getPos(){return this.pos}seekZero(){this.pos=0}slice(){return this.checkSize(),this.buffer.slice(0,this.pos)}subarray(){return this.pos===this.buffer.byteLength?this.buffer:(this.checkSize(),this.buffer.subarray(0,this.pos))}align4(){this.pos=this.pos+3&-4}set(e){this.buffer=new Uint8Array(e),this.seekZero()}pushBool(e){this.pushByte(Number(e))}getBool(){return Boolean(this.getByte())}setByte(e,t){this.buffer[e]=t}pushByte(e){this.pushUint8(e)}getByte(){return this.getUint8()}pushBytes(e,t=!1){this.pushLength(e.length),!0===t&&this.align4(),this.buffer.set(e,this.pos),this.pos+=e.byteLength}getBytes(e=!1){const t=this.getLength();!0===e&&this.align4();const s=this.buffer.subarray(this.pos,this.pos+t);return this.pos+=t,s}getUint32ArrayView(e){if(this.align4(),0===e)return j;const t=new Uint32Array(this.buffer.buffer,this.pos+this.buffer.byteOffset,e);return this.pos+=4*e,t}pushUint8(e){this.buffer[this.pos++]=e}getUint8(){return this.buffer[this.pos++]}pushUint16(e){this.buffer[this.pos++]=e>>>8,this.buffer[this.pos++]=e}getUint16(){return(this.buffer[this.pos++]<<8|this.buffer[this.pos++])>>>0}pushUint32(e){this.buffer[this.pos++]=e>>>24,this.buffer[this.pos++]=e>>>16,this.buffer[this.pos++]=e>>>8,this.buffer[this.pos++]=e}getUint32(){return(this.buffer[this.pos++]<<24>>>0)+(this.buffer[this.pos++]<<16|this.buffer[this.pos++]<<8|this.buffer[this.pos++])>>>0}pushUint32Array(e){this.pushLength(e.length);for(const t of e)this.pushUint32(t)}getUint32Array(){const e=this.getLength(),t=new Uint32Array(e);for(let s=0;s<e;s+=1)t[s]=this.getUint32();return t}pushUTF8(e){const t=E(e);this.pushLength(t.length);for(let e=0;e<t.length;e+=1)this.buffer[this.pos++]=t.charCodeAt(e)}getUTF8(){const e=this.getLength();return this.pos+=e,T(String.fromCharCode.apply(null,this.buffer.subarray(this.pos-e,this.pos)))}pushASCII(e){this.pushLength(e.length);for(let t=0;t<e.length;t+=1)this.buffer[this.pos++]=e.charCodeAt(t)}getASCII(){const e=this.getLength();return this.pos+=e,String.fromCharCode.apply(null,this.buffer.subarray(this.pos-e,this.pos))}pushNetworkRedirect(e){void 0!==this.compression?this.pushBytes(this.compression.networkRedirect.compress(e)):this.pushASCII(e)}getNetworkRedirect(){return void 0!==this.compression?this.compression.networkRedirect.decompress(this.getBytes()):this.getASCII()}pushNetworkHostname(e){void 0!==this.compression?this.pushBytes(this.compression.networkHostname.compress(e)):this.pushASCII(e)}getNetworkHostname(){return void 0!==this.compression?this.compression.networkHostname.decompress(this.getBytes()):this.getASCII()}pushNetworkCSP(e){void 0!==this.compression?this.pushBytes(this.compression.networkCSP.compress(e)):this.pushASCII(e)}getNetworkCSP(){return void 0!==this.compression?this.compression.networkCSP.decompress(this.getBytes()):this.getASCII()}pushNetworkFilter(e){void 0!==this.compression?this.pushBytes(this.compression.networkFilter.compress(e)):this.pushASCII(e)}getNetworkFilter(){return void 0!==this.compression?this.compression.networkFilter.decompress(this.getBytes()):this.getASCII()}pushCosmeticSelector(e){void 0!==this.compression?this.pushBytes(this.compression.cosmeticSelector.compress(e)):this.pushASCII(e)}getCosmeticSelector(){return void 0!==this.compression?this.compression.cosmeticSelector.decompress(this.getBytes()):this.getASCII()}pushRawCosmetic(e){void 0!==this.compression?this.pushBytes(this.compression.cosmeticRaw.compress(E(e))):this.pushUTF8(e)}getRawCosmetic(){return void 0!==this.compression?T(this.compression.cosmeticRaw.decompress(this.getBytes())):this.getUTF8()}pushRawNetwork(e){void 0!==this.compression?this.pushBytes(this.compression.networkRaw.compress(E(e))):this.pushUTF8(e)}getRawNetwork(){return void 0!==this.compression?T(this.compression.networkRaw.decompress(this.getBytes())):this.getUTF8()}checkSize(){if(0!==this.pos&&this.pos>this.buffer.byteLength)throw new Error(`StaticDataView too small: ${this.buffer.byteLength}, but required ${this.pos} bytes`)}pushLength(e){e<=127?this.pushUint8(e):(this.pushUint8(128),this.pushUint32(e))}getLength(){const e=this.getUint8();return 128===e?this.getUint32():e}}class W{static deserialize(e){return new W({debug:e.getBool(),enableCompression:e.getBool(),enableHtmlFiltering:e.getBool(),enableInMemoryCache:e.getBool(),enableMutationObserver:e.getBool(),enableOptimizations:e.getBool(),enablePushInjectionsOnNavigationEvents:e.getBool(),guessRequestTypeFromUrl:e.getBool(),integrityCheck:e.getBool(),loadCSPFilters:e.getBool(),loadCosmeticFilters:e.getBool(),loadExceptionFilters:e.getBool(),loadExtendedSelectors:e.getBool(),loadGenericCosmeticsFilters:e.getBool(),loadNetworkFilters:e.getBool(),loadPreprocessors:e.getBool()})}constructor({debug:e=!1,enableCompression:t=!1,enableHtmlFiltering:s=!1,enableInMemoryCache:i=!0,enableMutationObserver:r=!0,enableOptimizations:o=!0,enablePushInjectionsOnNavigationEvents:n=!0,guessRequestTypeFromUrl:a=!1,integrityCheck:c=!0,loadCSPFilters:l=!0,loadCosmeticFilters:h=!0,loadExceptionFilters:u=!0,loadExtendedSelectors:d=!1,loadGenericCosmeticsFilters:f=!0,loadNetworkFilters:p=!0,loadPreprocessors:g=!1}={}){this.debug=e,this.enableCompression=t,this.enableHtmlFiltering=s,this.enableInMemoryCache=i,this.enableMutationObserver=r,this.enableOptimizations=o,this.enablePushInjectionsOnNavigationEvents=n,this.guessRequestTypeFromUrl=a,this.integrityCheck=c,this.loadCSPFilters=l,this.loadCosmeticFilters=h,this.loadExceptionFilters=u,this.loadExtendedSelectors=d,this.loadGenericCosmeticsFilters=f,this.loadNetworkFilters=p,this.loadPreprocessors=g}getSerializedSize(){return 16}serialize(e){e.pushBool(this.debug),e.pushBool(this.enableCompression),e.pushBool(this.enableHtmlFiltering),e.pushBool(this.enableInMemoryCache),e.pushBool(this.enableMutationObserver),e.pushBool(this.enableOptimizations),e.pushBool(this.enablePushInjectionsOnNavigationEvents),e.pushBool(this.guessRequestTypeFromUrl),e.pushBool(this.integrityCheck),e.pushBool(this.loadCSPFilters),e.pushBool(this.loadCosmeticFilters),e.pushBool(this.loadExceptionFilters),e.pushBool(this.loadExtendedSelectors),e.pushBool(this.loadGenericCosmeticsFilters),e.pushBool(this.loadNetworkFilters),e.pushBool(this.loadPreprocessors)}}const G="undefined"!=typeof window&&"function"==typeof window.queueMicrotask?e=>window.queueMicrotask(e):e=>(L||(L=Promise.resolve())).then(e).catch((e=>setTimeout((()=>{throw e}),0)));function X(e,t,s){let i=s.get(e);void 0===i&&(i=[],s.set(e,i)),i.push(t)}function K(e,t,s){const i=s.get(e);if(void 0!==i){const e=i.indexOf(t);-1!==e&&i.splice(e,1)}}function Z(e,t,s){if(0===s.size)return!1;const i=s.get(e);return void 0!==i&&(G((()=>{for(const e of i)e(...t)})),!0)}class Y{constructor(){this.onceListeners=new Map,this.onListeners=new Map}on(e,t){X(e,t,this.onListeners)}once(e,t){X(e,t,this.onceListeners)}unsubscribe(e,t){K(e,t,this.onListeners),K(e,t,this.onceListeners)}emit(e,...t){Z(e,t,this.onListeners),!0===Z(e,t,this.onceListeners)&&this.onceListeners.delete(e)}}function Q(e,t){let s=3;const i=()=>e(t).catch((e=>{if(s>0)return s-=1,new Promise(((e,t)=>{setTimeout((()=>{i().then(e).catch(t)}),500)}));throw e}));return i()}function J(e,t){return Q(e,t).then((e=>e.text()))}const ee="https://raw.githubusercontent.com/ghostery/adblocker/master/packages/adblocker/assets",te=[`${ee}/easylist/easylist.txt`,`${ee}/peter-lowe/serverlist.txt`,`${ee}/ublock-origin/badware.txt`,`${ee}/ublock-origin/filters-2020.txt`,`${ee}/ublock-origin/filters-2021.txt`,`${ee}/ublock-origin/filters-2022.txt`,`${ee}/ublock-origin/filters-2023.txt`,`${ee}/ublock-origin/filters-2024.txt`,`${ee}/ublock-origin/filters.txt`,`${ee}/ublock-origin/quick-fixes.txt`,`${ee}/ublock-origin/resource-abuse.txt`,`${ee}/ublock-origin/unbreak.txt`],se=[...te,`${ee}/easylist/easyprivacy.txt`,`${ee}/ublock-origin/privacy.txt`],ie=[...se,`${ee}/easylist/easylist-cookie.txt`,`${ee}/ublock-origin/annoyances-others.txt`,`${ee}/ublock-origin/annoyances-cookies.txt`];function re(e,t){return Promise.all(t.map((t=>J(e,t))))}function oe(e){return J(e,`${ee}/ublock-origin/resources.json`)}class ne{constructor(){this.options=new Set,this.prefix=void 0,this.infix=void 0,this.suffix=void 0,this.redirect=void 0}blockRequestsWithType(e){if(this.options.has(e))throw new Error(`Already blocking type ${e}`);return this.options.add(e),this}images(){return this.blockRequestsWithType("image")}scripts(){return this.blockRequestsWithType("script")}frames(){return this.blockRequestsWithType("frame")}fonts(){return this.blockRequestsWithType("font")}medias(){return this.blockRequestsWithType("media")}styles(){return this.blockRequestsWithType("css")}redirectTo(e){if(void 0!==this.redirect)throw new Error(`Already redirecting: ${this.redirect}`);return this.redirect=`redirect=${e}`,this}urlContains(e){if(void 0!==this.infix)throw new Error(`Already matching pattern: ${this.infix}`);return this.infix=e,this}urlStartsWith(e){if(void 0!==this.prefix)throw new Error(`Already matching prefix: ${this.prefix}`);return this.prefix=`|${e}`,this}urlEndsWith(e){if(void 0!==this.suffix)throw new Error(`Already matching suffix: ${this.suffix}`);return this.suffix=`${e}|`,this}withHostname(e){if(void 0!==this.prefix)throw new Error(`Cannot match hostname if filter already has prefix: ${this.prefix}`);return this.prefix=`||${e}^`,this}toString(){const e=[];void 0!==this.prefix&&e.push(this.prefix),void 0!==this.infix&&e.push(this.infix),void 0!==this.suffix&&e.push(this.suffix);const t=["important"];if(0!==this.options.size)for(const e of this.options)t.push(e);return void 0!==this.redirect&&t.push(this.redirect),`${0===e.length?"*":e.join("*")}$${t.join(",")}`}}function ae(){return new ne}const ce=new class{constructor(e){this.pos=0,this.buffer=new Uint32Array(e)}reset(){this.pos=0}slice(){return this.buffer.slice(0,this.pos)}push(e){this.buffer[this.pos++]=e}empty(){return 0===this.pos}full(){return this.pos===this.buffer.length}remaining(){return this.buffer.length-this.pos}}(1024),le=37,he=5011;function ue(e){return 16843009*((e=(858993459&(e-=e>>1&1431655765))+(e>>2&858993459))+(e>>4)&252645135)>>24}function de(e,t){return!!(e&t)}function fe(e,t){return e|t}function pe(e,t){return e&~t}function ge(e,t,s){let i=he;for(let r=t;r<s;r+=1)i=i*le^e.charCodeAt(r);return i>>>0}function me(e){return"string"!=typeof e||0===e.length?he:ge(e,0,e.length)}function ye(e){const t=new Uint32Array(e.length);let s=0;for(const i of e)t[s++]=me(i);return t}function we(e,t){if(e.length<t.length)return!1;const s=t.length;for(let i=0;i<s;i+=1)if(e[i]!==t[i])return!1;return!0}function be(e,t,s){if(e.length-s<t.length)return!1;const i=s+t.length;for(let r=s;r<i;r+=1)if(e[r]!==t[r-s])return!1;return!0}function ve(e){return e>=48&&e<=57}function ke(e){return e>=97&&e<=122||e>=65&&e<=90}function xe(e){return ke(e)||ve(e)||37===e||function(e){return e>=192&&e<=450}(e)||function(e){return e>=1024&&e<=1279}(e)}function Se(e,t,s,i){const r=Math.min(e.length,2*i.remaining());let o=!1,n=0,a=he;for(let s=0;s<r;s+=1){const r=e.charCodeAt(s);!0===xe(r)?(!1===o&&(a=he,o=!0,n=s),a=a*le^r):!0===o&&(o=!1,s-n>1&&(!1===t||0!==n)&&i.push(a>>>0))}!0===o&&!1===s&&e.length-n>1&&!1===i.full()&&i.push(a>>>0)}function Fe(e,t){const s=Math.min(e.length,2*t.remaining());let i=!1,r=0,o=he;for(let n=0;n<s;n+=1){const s=e.charCodeAt(n);!0===xe(s)?(!1===i&&(o=he,i=!0,r=n),o=o*le^s):!0===i&&(i=!1,n-r>1&&t.push(o>>>0))}!0===i&&e.length-r>1&&!1===t.full()&&t.push(o>>>0)}function Ae(e){return ce.reset(),Fe(e,ce),ce.slice()}function ze(e,t){return-1!==function(e,t){if(0===e.length)return-1;let s=0,i=e.length-1;for(;s<=i;){const r=s+i>>>1,o=e[r];if(o<t)s=r+1;else{if(!(o>t))return r;i=r-1}}return-1}(e,t)}const Ie=/[^\u0000-\u00ff]/;function Ce(e){return Ie.test(e)}const Re={extractHostname:!0,mixedInputs:!1,validateHostname:!1},Ue={beacon:me("type:beacon"),cspReport:me("type:csp"),csp_report:me("type:csp"),cspviolationreport:me("type:cspviolationreport"),document:me("type:document"),eventsource:me("type:other"),fetch:me("type:xhr"),font:me("type:font"),image:me("type:image"),imageset:me("type:image"),mainFrame:me("type:document"),main_frame:me("type:document"),manifest:me("type:other"),media:me("type:media"),object:me("type:object"),object_subrequest:me("type:object"),other:me("type:other"),ping:me("type:ping"),prefetch:me("type:other"),preflight:me("type:preflight"),script:me("type:script"),signedexchange:me("type:signedexchange"),speculative:me("type:other"),stylesheet:me("type:stylesheet"),subFrame:me("type:subdocument"),sub_frame:me("type:subdocument"),texttrack:me("type:other"),webSocket:me("type:websocket"),web_manifest:me("type:other"),websocket:me("type:websocket"),xhr:me("type:xhr"),xml_dtd:me("type:other"),xmlhttprequest:me("type:xhr"),xslt:me("type:other")};function Te(e){let t=he;for(let s=e.length-1;s>=0;s-=1)t=t*le^e.charCodeAt(s);return t>>>0}function Ee(e,t,s){ce.reset();let i=he;for(let r=t-1;r>=0;r-=1){const t=e.charCodeAt(r);46===t&&r<s&&ce.push(i>>>0),i=i*le^t}return ce.push(i>>>0),ce.slice()}function Oe(e,t){const s=function(e,t){let s=null;const i=t.indexOf(".");if(-1!==i){const r=t.slice(i+1);s=e.slice(0,-r.length-1)}return s}(e,t);return null!==s?Ee(s,s.length,s.length):j}function He(e,t){return Ee(e,e.length,e.length-t.length)}class je{static fromRawDetails({requestId:e="0",tabId:t=0,url:s="",hostname:i,domain:o,sourceUrl:n="",sourceHostname:a,sourceDomain:c,type:l="main_frame",_originalRequestDetails:h}){if(s=s.toLowerCase(),void 0===i||void 0===o){const e=r.parse(s,Re);i=i||e.hostname||"",o=o||e.domain||""}if(void 0===a||void 0===c){const e=r.parse(a||c||n,Re);a=a||e.hostname||"",c=c||e.domain||a||""}return new je({requestId:e,tabId:t,domain:o,hostname:i,url:s,sourceDomain:c,sourceHostname:a,sourceUrl:n,type:l,_originalRequestDetails:h})}constructor({requestId:e,tabId:t,type:s,domain:i,hostname:r,url:o,sourceDomain:n,sourceHostname:a,_originalRequestDetails:c}){if(this.tokens=void 0,this.hostnameHashes=void 0,this.entityHashes=void 0,this._originalRequestDetails=c,this.id=e,this.tabId=t,this.type=s,this.url=o,this.hostname=r,this.domain=i,this.sourceHostnameHashes=0===a.length?j:He(a,n),this.sourceEntityHashes=0===a.length?j:Oe(a,n),this.isThirdParty=function(e,t,s,i,r){return"main_frame"!==r&&"mainFrame"!==r&&(0!==t.length&&0!==i.length?t!==i:0!==t.length&&0!==s.length?t!==s:0!==i.length&&0!==e.length&&e!==i)}(r,i,a,n,s),this.isFirstParty=!this.isThirdParty,this.isSupported=!0,"websocket"===this.type||this.url.startsWith("ws:")||this.url.startsWith("wss:"))this.isHttp=!1,this.isHttps=!1,this.type="websocket",this.isSupported=!0;else if(this.url.startsWith("http:"))this.isHttp=!0,this.isHttps=!1;else if(this.url.startsWith("https:"))this.isHttps=!0,this.isHttp=!1;else if(this.url.startsWith("data:")){this.isHttp=!1,this.isHttps=!1;const e=this.url.indexOf(",");-1!==e&&(this.url=this.url.slice(0,e))}else this.isHttp=!1,this.isHttps=!1,this.isSupported=!1}getHostnameHashes(){return void 0===this.hostnameHashes&&(this.hostnameHashes=0===this.hostname.length?j:He(this.hostname,this.domain)),this.hostnameHashes}getEntityHashes(){return void 0===this.entityHashes&&(this.entityHashes=0===this.hostname.length?j:Oe(this.hostname,this.domain)),this.entityHashes}getTokens(){if(void 0===this.tokens){ce.reset();for(const e of this.sourceHostnameHashes)ce.push(e);ce.push(Ue[this.type]),Fe(this.url,ce),this.tokens=ce.slice()}return this.tokens}isMainFrame(){return"main_frame"===this.type||"mainFrame"===this.type}isSubFrame(){return"sub_frame"===this.type||"subFrame"===this.type}guessTypeOfRequest(){const e=this.type;return this.type=i(this.url),e!==this.type&&(this.tokens=void 0),this.type}}class Pe{static parse(e,t=!1){if(0===e.length)return;const s=[],i=[],r=[],o=[];for(let t of e){Ce(t)&&(t=O(t));const e=126===t.charCodeAt(0),n=42===t.charCodeAt(t.length-1)&&46===t.charCodeAt(t.length-2),a=e?1:0,c=n?t.length-2:t.length,l=Te(!0===e||!0===n?t.slice(a,c):t);e?n?i.push(l):o.push(l):n?s.push(l):r.push(l)}return new Pe({entities:0!==s.length?new Uint32Array(s).sort():void 0,hostnames:0!==r.length?new Uint32Array(r).sort():void 0,notEntities:0!==i.length?new Uint32Array(i).sort():void 0,notHostnames:0!==o.length?new Uint32Array(o).sort():void 0,parts:!0===t?e.join(","):void 0})}static deserialize(e){const t=e.getUint8();return new Pe({entities:1&~t?void 0:e.getUint32Array(),hostnames:2&~t?void 0:e.getUint32Array(),notEntities:4&~t?void 0:e.getUint32Array(),notHostnames:8&~t?void 0:e.getUint32Array(),parts:16&~t?void 0:e.getUTF8()})}constructor({entities:e,hostnames:t,notEntities:s,notHostnames:i,parts:r}){this.entities=e,this.hostnames=t,this.notEntities=s,this.notHostnames=i,this.parts=r}updateId(e){const{hostnames:t,entities:s,notHostnames:i,notEntities:r}=this;if(void 0!==t)for(const s of t)e=e*le^s;if(void 0!==s)for(const t of s)e=e*le^t;if(void 0!==i)for(const t of i)e=e*le^t;if(void 0!==r)for(const t of r)e=e*le^t;return e}serialize(e){const t=e.getPos();e.pushUint8(0);let s=0;void 0!==this.entities&&(s|=1,e.pushUint32Array(this.entities)),void 0!==this.hostnames&&(s|=2,e.pushUint32Array(this.hostnames)),void 0!==this.notEntities&&(s|=4,e.pushUint32Array(this.notEntities)),void 0!==this.notHostnames&&(s|=8,e.pushUint32Array(this.notHostnames)),void 0!==this.parts&&(s|=16,e.pushUTF8(this.parts)),e.setByte(t,s)}getSerializedSize(){let e=1;return void 0!==this.entities&&(e+=q(this.entities)),void 0!==this.hostnames&&(e+=q(this.hostnames)),void 0!==this.notHostnames&&(e+=q(this.notHostnames)),void 0!==this.notEntities&&(e+=q(this.notEntities)),void 0!==this.parts&&(e+=$(this.parts)),e}match(e,t){if(void 0!==this.notHostnames)for(const t of e)if(ze(this.notHostnames,t))return!1;if(void 0!==this.notEntities)for(const e of t)if(ze(this.notEntities,e))return!1;if(void 0!==this.hostnames||void 0!==this.entities){if(void 0!==this.hostnames)for(const t of e)if(ze(this.hostnames,t))return!0;if(void 0!==this.entities)for(const e of t)if(ze(this.entities,e))return!0;return!1}return!0}}function Le(e){if(!1===e.startsWith("^script"))return;const t=":has-text(",s=[];let i=7;for(;e.startsWith(t,i);){i+=10;let t=1;const r=i;let o=-1;for(;i<e.length&&0!==t;i+=1){const s=e.charCodeAt(i);92!==o&&(40===s&&(t+=1),41===s&&(t-=1)),o=s}s.push(e.slice(r,i-1))}return i===e.length?["script",s]:void 0}function De(e,t){const s=[],i=`<${t}`,r=`</${t}>`;let o=e.indexOf(i),n=0;for(;-1!==o;){const t=e.indexOf(">",o+i.length);if(-1===t)return[s,e.slice(0,o),e.slice(o)];if(47===e.charCodeAt(t-1))n=t+1,s.push([o,e.slice(o,n)]);else{const i=e.indexOf(r,t);if(-1===i)return[s,e.slice(0,o),e.slice(o)];s.push([o,e.slice(o,i+r.length)]),n=i+r.length}o=e.indexOf(i,n)}let a=e.lastIndexOf(">");-1===a&&(a=n);const c=e.indexOf("<",a);return-1===c||e.length-c>=i.length||!1===i.startsWith(e.slice(c))?[s,e,""]:[s,e.slice(0,c),e.slice(c)]}function Be(e,t,s){for(const s of t)if(-1===e.indexOf(s))return!1;for(const t of s)if(!1===t.test(e))return!1;return!0}function Ne(e,t){const s=[];for(const i of t)for(const[t,r]of e)if(Be(i[1],t,r)){s.push(i);break}return s}function Me(e,t){if(0===t.length)return e;let s=e;t.reverse();for(const[e,i]of t)s=s.slice(0,e)+s.slice(e+i.length);return s}const _e=[j],$e="display: none !important;",qe=new RegExp(/\\u002C/,"g"),Ve=new RegExp(/\\u005C/,"g"),We=new RegExp(/\\,/,"g");function Ge(e){for(let t=1;t<e.length;t+=1){const s=e.charCodeAt(t);if(!(45===s||95===s||s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122)){if(t<e.length-1){const i=e.charCodeAt(t+1);if(91===s||46===s||58===s||32===s&&(62===i||43===i||126===i||46===i||35===i))return!0}return!1}}return!0}function Xe(e,t){return e.startsWith('href^="',t)||e.startsWith('href*="',t)||e.startsWith('href="',t)}const Ke=(()=>{const e="undefined"!=typeof document?document.createElement("div"):{matches:()=>{}},t=/^[#.]?[\w-.]+$/;return function(s){if(t.test(s))return!0;try{(t=>{e.matches(t)})(s)}catch(e){return!1}return!0}})();var Ze;!function(e){e[e.unhide=1]="unhide",e[e.scriptInject=2]="scriptInject",e[e.isUnicode=4]="isUnicode",e[e.isClassSelector=8]="isClassSelector",e[e.isIdSelector=16]="isIdSelector",e[e.isHrefSelector=32]="isHrefSelector",e[e.remove=64]="remove",e[e.extended=128]="extended"}(Ze||(Ze={}));class Ye{static parse(e,t=!1){const i=e;let r,o,n,a=0;const c=e.indexOf("#"),l=c+1;let h=l+1;if(e.length>l&&("@"===e[l]?(a=fe(a,Ze.unhide),h+=1):"?"===e[l]&&(h+=1)),h>=e.length)return null;if(c>0&&(o=Pe.parse(e.slice(0,c).split(","),t)),e.endsWith(":remove()"))a=fe(a,Ze.remove),a=fe(a,Ze.extended),e=e.slice(0,-9);else if(e.length-h>=8&&e.endsWith(")")&&-1!==e.indexOf(":style(",h)){const t=e.indexOf(":style(",h);n=e.slice(t+7,-1),e=e.slice(0,t)}if(94===e.charCodeAt(h)){if(!1===be(e,"script:has-text(",h+1)||41!==e.charCodeAt(e.length-1))return null;if(r=e.slice(h,e.length),void 0===Le(r))return null}else if(e.length-h>4&&43===e.charCodeAt(h)&&be(e,"+js(",h)){if((void 0===o||void 0===o.hostnames&&void 0===o.entities)&&!1===de(a,Ze.unhide))return null;if(a=fe(a,Ze.scriptInject),r=e.slice(h+4,e.length-1),!1===de(a,Ze.unhide)&&0===r.length)return null}else{r=e.slice(h);const t=s.classifySelector(r);if(t===s.SelectorType.Extended)a=fe(a,Ze.extended);else if(t===s.SelectorType.Invalid||!Ke(r))return null}if(void 0===o&&!0===de(a,Ze.extended))return null;if(void 0!==r&&(Ce(r)&&(a=fe(a,Ze.isUnicode)),!1===de(a,Ze.scriptInject)&&!1===de(a,Ze.remove)&&!1===de(a,Ze.extended)&&!1===r.startsWith("^"))){const e=r.charCodeAt(0),t=r.charCodeAt(1),s=r.charCodeAt(2);!1===de(a,Ze.scriptInject)&&(46===e&&Ge(r)?a=fe(a,Ze.isClassSelector):35===e&&Ge(r)?a=fe(a,Ze.isIdSelector):(97===e&&91===t&&104===s&&Xe(r,2)||91===e&&104===t&&Xe(r,1))&&(a=fe(a,Ze.isHrefSelector)))}return new Ye({mask:a,rawLine:!0===t?i:void 0,selector:r,style:n,domains:o})}static deserialize(e){const t=e.getUint8(),s=de(t,Ze.isUnicode),i=e.getUint8(),r=s?e.getUTF8():e.getCosmeticSelector();return new Ye({mask:t,selector:r,domains:1&~i?void 0:Pe.deserialize(e),rawLine:2&~i?void 0:e.getRawCosmetic(),style:4&~i?void 0:e.getASCII()})}constructor({mask:e,selector:t,domains:s,rawLine:i,style:r}){this.mask=e,this.selector=t,this.domains=s,this.style=r,this.id=void 0,this.rawLine=i}isCosmeticFilter(){return!0}isNetworkFilter(){return!1}serialize(e){e.pushUint8(this.mask);const t=e.getPos();e.pushUint8(0),this.isUnicode()?e.pushUTF8(this.selector):e.pushCosmeticSelector(this.selector);let s=0;void 0!==this.domains&&(s|=1,this.domains.serialize(e)),void 0!==this.rawLine&&(s|=2,e.pushRawCosmetic(this.rawLine)),void 0!==this.style&&(s|=4,e.pushASCII(this.style)),e.setByte(t,s)}getSerializedSize(e){let t=2;return this.isUnicode()?t+=$(this.selector):t+=function(e,t){return!0===t?M(D().cosmeticSelector.getCompressedSize(e),!1):_(e)}(this.selector,e),void 0!==this.domains&&(t+=this.domains.getSerializedSize()),void 0!==this.rawLine&&(t+=function(e,t){return!0===t?M(D().cosmeticRaw.getCompressedSize(E(e)),!1):$(e)}(this.rawLine,e)),void 0!==this.style&&(t+=_(this.style)),t}toString(){if(void 0!==this.rawLine)return this.rawLine;let e="";return void 0!==this.domains&&(void 0!==this.domains.parts?e+=this.domains.parts:e+="<hostnames>"),this.isUnhide()?e+="#@#":e+="##",this.isScriptInject()?(e+="+js(",e+=this.selector,e+=")"):e+=this.selector,e}match(e,t){return!1===this.hasHostnameConstraint()||!(!e&&this.hasHostnameConstraint())&&(void 0===this.domains||this.domains.match(0===e.length?j:He(e,t),0===e.length?j:Oe(e,t)))}getTokens(){const e=[];if(void 0!==this.domains){const{hostnames:t,entities:s}=this.domains;if(void 0!==t)for(const s of t)e.push(new Uint32Array([s]));if(void 0!==s)for(const t of s)e.push(new Uint32Array([t]))}if(0===e.length&&!1===this.isUnhide())if(this.isIdSelector()||this.isClassSelector()){let t=1;const s=this.selector;for(;t<s.length;t+=1){const e=s.charCodeAt(t);if(32===e||46===e||58===e||91===e)break}const i=new Uint32Array(1);i[0]=ge(s,1,t),e.push(i)}else if(!0===this.isHrefSelector()){const t=this.getSelector();let s=t.indexOf("href");if(-1===s)return _e;s+=4;let i=!1,r=!0;42===t.charCodeAt(s)?(i=!0,s+=1):94===t.charCodeAt(s)?s+=1:r=!1,s+=2;const o=t.indexOf('"',s);if(-1===o)return _e;e.push(function(e,t,s){return ce.reset(),Se(e,t,s,ce),ce.slice()}(this.selector.slice(s,o),i,r))}return 0===e.length?_e:e}parseScript(){const e=this.getSelector();if(0===e.length)return;const t=[];let s=0,i=-1,r=!1,o=!1,n=!1,a=0,c=!1,l=!1;for(;s<e.length;s+=1){const h=e[s];!1===c&&(!0===r?'"'===h&&(r=!1):!0===o?"'"===h&&(o=!1):0!==a?"{"===h?a+=1:"}"===h?a-=1:'"'===h?r=!0:"'"===h&&(o=!0):!0===n?"/"===h&&(n=!1):(!1===l&&(" "===h||('"'===h&&e.indexOf('"',s+1)>0?r=!0:"'"===h&&e.indexOf("'",s+1)>0?o=!0:"{"===h&&e.indexOf("}",s+1)>0?a+=1:"/"===h&&e.indexOf("/",s+1)>0?n=!0:l=!0)),","===h&&(t.push(e.slice(i+1,s).trim()),i=s,l=!1))),c="\\"===h}if(t.push(e.slice(i+1).trim()),0===t.length)return;const h=t.slice(1).map((e=>e.startsWith("'")&&e.endsWith("'")||e.startsWith('"')&&e.endsWith('"')?e.substring(1,e.length-1):e)).map((e=>e.replace(qe,",").replace(Ve,"\\").replace(We,",")));return{name:t[0],args:h}}getScript(e){const t=this.parseScript();if(void 0===t)return;const{name:s,args:i}=t;let r=e(s);if(void 0!==r){for(let e=0;e<i.length;e+=1){const t=i[e].replace(/[.*+?^${}()|[\]\\]/g,"\\$&");r=r.replace(`{{${e+1}}}`,t)}return r}}hasHostnameConstraint(){return void 0!==this.domains}getId(){return void 0===this.id&&(this.id=function(e,t,s,i){let r=he*le^e;if(void 0!==t)for(let e=0;e<t.length;e+=1)r=r*le^t.charCodeAt(e);if(void 0!==s&&(r=s.updateId(r)),void 0!==i)for(let e=0;e<i.length;e+=1)r=r*le^i.charCodeAt(e);return r>>>0}(this.mask,this.selector,this.domains,this.style)),this.id}hasCustomStyle(){return void 0!==this.style}getStyle(e=$e){return this.style||e}getStyleAttributeHash(){return`s${me(this.getStyle())}`}getSelector(){return this.selector}getSelectorAST(){return s.parse(this.getSelector())}getExtendedSelector(){return Le(this.selector)}isExtended(){return de(this.mask,Ze.extended)}isRemove(){return de(this.mask,Ze.remove)}isUnhide(){return de(this.mask,Ze.unhide)}isScriptInject(){return de(this.mask,Ze.scriptInject)}isCSS(){return!1===this.isScriptInject()}isIdSelector(){return de(this.mask,Ze.isIdSelector)}isClassSelector(){return de(this.mask,Ze.isClassSelector)}isHrefSelector(){return de(this.mask,Ze.isHrefSelector)}isUnicode(){return de(this.mask,Ze.isUnicode)}isHtmlFiltering(){return this.getSelector().startsWith("^")}isGenericHide(){var e,t;return void 0===(null===(e=null==this?void 0:this.domains)||void 0===e?void 0:e.hostnames)&&void 0===(null===(t=null==this?void 0:this.domains)||void 0===t?void 0:t.entities)}}const Qe=me("http"),Je=me("https");var et;!function(e){e[e.fromDocument=1]="fromDocument",e[e.fromFont=2]="fromFont",e[e.fromHttp=4]="fromHttp",e[e.fromHttps=8]="fromHttps",e[e.fromImage=16]="fromImage",e[e.fromMedia=32]="fromMedia",e[e.fromObject=64]="fromObject",e[e.fromOther=128]="fromOther",e[e.fromPing=256]="fromPing",e[e.fromScript=512]="fromScript",e[e.fromStylesheet=1024]="fromStylesheet",e[e.fromSubdocument=2048]="fromSubdocument",e[e.fromWebsocket=4096]="fromWebsocket",e[e.fromXmlHttpRequest=8192]="fromXmlHttpRequest",e[e.firstParty=16384]="firstParty",e[e.thirdParty=32768]="thirdParty",e[e.isReplace=65536]="isReplace",e[e.isBadFilter=131072]="isBadFilter",e[e.isCSP=262144]="isCSP",e[e.isGenericHide=524288]="isGenericHide",e[e.isImportant=1048576]="isImportant",e[e.isSpecificHide=2097152]="isSpecificHide",e[e.isFullRegex=4194304]="isFullRegex",e[e.isRegex=8388608]="isRegex",e[e.isUnicode=16777216]="isUnicode",e[e.isLeftAnchor=33554432]="isLeftAnchor",e[e.isRightAnchor=67108864]="isRightAnchor",e[e.isException=134217728]="isException",e[e.isHostnameAnchor=268435456]="isHostnameAnchor",e[e.isRedirectRule=536870912]="isRedirectRule",e[e.isRedirect=1073741824]="isRedirect"}(et||(et={}));const tt=et.fromDocument|et.fromFont|et.fromImage|et.fromMedia|et.fromObject|et.fromOther|et.fromPing|et.fromScript|et.fromStylesheet|et.fromSubdocument|et.fromWebsocket|et.fromXmlHttpRequest,st={beacon:et.fromPing,document:et.fromDocument,cspviolationreport:et.fromOther,fetch:et.fromXmlHttpRequest,font:et.fromFont,image:et.fromImage,imageset:et.fromImage,mainFrame:et.fromDocument,main_frame:et.fromDocument,media:et.fromMedia,object:et.fromObject,object_subrequest:et.fromObject,ping:et.fromPing,script:et.fromScript,stylesheet:et.fromStylesheet,subFrame:et.fromSubdocument,sub_frame:et.fromSubdocument,webSocket:et.fromWebsocket,websocket:et.fromWebsocket,xhr:et.fromXmlHttpRequest,xmlhttprequest:et.fromXmlHttpRequest,cspReport:et.fromOther,csp_report:et.fromOther,eventsource:et.fromOther,manifest:et.fromOther,other:et.fromOther,prefetch:et.fromOther,preflight:et.fromOther,signedexchange:et.fromOther,speculative:et.fromOther,texttrack:et.fromOther,web_manifest:et.fromOther,xml_dtd:et.fromOther,xslt:et.fromOther};function it(e){const t=[];return e.fromDocument()&&t.push("document"),e.fromImage()&&t.push("image"),e.fromMedia()&&t.push("media"),e.fromObject()&&t.push("object"),e.fromOther()&&t.push("other"),e.fromPing()&&t.push("ping"),e.fromScript()&&t.push("script"),e.fromStylesheet()&&t.push("stylesheet"),e.fromSubdocument()&&t.push("sub_frame"),e.fromWebsocket()&&t.push("websocket"),e.fromXmlHttpRequest()&&t.push("xhr"),e.fromFont()&&t.push("font"),t}function rt(e,t,s,i,r,o){let n=he*le^e;if(void 0!==i&&(n=i.updateId(n)),void 0!==r&&(n=r.updateId(n)),void 0!==t)for(let e=0;e<t.length;e+=1)n=n*le^t.charCodeAt(e);if(void 0!==s)for(let e=0;e<s.length;e+=1)n=n*le^s.charCodeAt(e);if(void 0!==o)for(let e=0;e<o.length;e+=1)n=n*le^o.charCodeAt(e);return n>>>0}function ot(e,t,s,i){return!0===i?new RegExp(e.slice(1,e.length-1),"i"):(e=(e=(e=e.replace(/([|.$+?{}()[\]\\])/g,"\\$1")).replace(/\*/g,".*")).replace(/\^/g,"(?:[^\\w\\d_.%-]|$)"),s&&(e=`${e}$`),t&&(e=`^${e}`),new RegExp(e))}function nt(e,t,s){const i=t;for(;t<s;t++){const i=e.charCodeAt(t);if(61===i||44===i){s=t;break}}return[t,e.slice(i,s)]}function at(e,t,s){let i=t,r="";for(;t<s;t++){const s=e.charCodeAt(t);if(92===s)r+=e.slice(i,t),i=++t;else if(44===s)break}return i-t!=0&&(r+=e.slice(i,t)),[t,r]}const ct=new Set([102,110,114,116,118,48,94,36,92,46,42,43,63,40,41,91,93,123,125,124,47,100,68,119,87,115,83,98,66]);function lt(e){return e>=48&&e<=57||e<=65&&e<=70||e>=97&&e<=102}function ht(e,t,s){const i=e.charCodeAt(t+1);return 44===i||47===i?[t+1,!1]:function(e,t,s){const i=e.charCodeAt(t+1);if(44===i||ct.has(i))return[t+1,!0];if(99===i){const s=e.charCodeAt(t+2);if(s>=65&&s<=90||s>=97&&s<=122)return[t+2,!0]}if(120===i&&lt(e.charCodeAt(t+2))&&lt(e.charCodeAt(t+3)))return[t+3,!0];if(117===i)if(123===e.charCodeAt(t+2)){const s=e.indexOf("}",t+3),i=s-t+3;if(i>=1&&i<=6)return[s,!0]}else if(lt(e.charCodeAt(t+2))&&lt(e.charCodeAt(t+3))&&lt(e.charCodeAt(t+4))&&lt(e.charCodeAt(t+5)))return[t+5,!0];return[t+1,!1]}(e,t)}function ut(e,t,s){if(47!==e.charCodeAt(t++))return[s,void 0];const i=["","",""];let r=t,o=0;for(;t<s;t++){const s=e.charCodeAt(t);if(92===s){i[o]+=e.slice(r,t);const[s,n]=ht(e,t);!1===n&&++t,r=t,t=s}else if(47===s&&(t-r!=0&&(i[o]+=e.slice(r,t)),r=t+1,2==++o))break}const n=e.indexOf(",",t);return-1!==n&&(s=n),i[2]=e.slice(r,s),[t=s,i]}function dt(e){const[,t]=ut(e,0,e.length);if(void 0===t)return null;try{return[new RegExp(t[0],t[2]),t[1]]}catch(e){return null}}const ft=new RegExp("");class pt{static parse(e,t=!1){let s,i,r,o,n=et.thirdParty|et.firstParty|et.fromHttps|et.fromHttp,a=0,c=tt,l=0,h=e.length;64===e.charCodeAt(0)&&64===e.charCodeAt(1)&&(l+=2,n=fe(n,et.isException));const u=function(e,t){let s=e.lastIndexOf(t);if(-1===s)return-1;for(;s>0&&92===e.charCodeAt(s-1);)s=e.lastIndexOf(t,s-1);return s}(e,"$");if(-1!==u&&47!==e.charCodeAt(u+1)){h=u;for(const s of function(e,t,s){const i=[];let r,o;for(;t<s;t++)if([t,r]=nt(e,t,s),void 0!==r){if(61===e.charCodeAt(t)&&t++,"replace"===r){const i=ut(e,t,s);o=void 0===i[1]?"":e.slice(t,i[0]),t=i[0]}else[t,o]=at(e,t,s);i.push([r,o])}return i}(e,u+1,e.length)){const e=126===s[0].charCodeAt(0),l=!0===e?s[0].slice(1):s[0],h=s[1];switch(l){case"denyallow":r=Pe.parse(h.split("|"),t);break;case"domain":case"from":if(124===h.charCodeAt(0)||124===h.charCodeAt(h.length-1))return null;i=Pe.parse(h.split("|"),t);break;case"badfilter":n=fe(n,et.isBadFilter);break;case"important":if(e)return null;n=fe(n,et.isImportant);break;case"match-case":if(e)return null;break;case"3p":case"third-party":n=pe(n,e?et.thirdParty:et.firstParty);break;case"1p":case"first-party":n=pe(n,e?et.firstParty:et.thirdParty);break;case"redirect-rule":case"redirect":{if(e)return null;if(0===h.length)return null;const t=h.lastIndexOf(":");if(0===t)return null;if(-1!==t&&(!0===isNaN(Number(h.slice(t+1)))||t+1===h.length))return null;n=fe(n,et.isRedirect),"redirect-rule"===l&&(n=fe(n,et.isRedirectRule)),o=h;break}case"csp":if(e)return null;n=fe(n,et.isCSP),h.length>0&&(o=h);break;case"ehide":case"elemhide":if(e)return null;n=fe(n,et.isGenericHide),n=fe(n,et.isSpecificHide);break;case"shide":case"specifichide":if(e)return null;n=fe(n,et.isSpecificHide);break;case"ghide":case"generichide":if(e)return null;n=fe(n,et.isGenericHide);break;case"inline-script":if(e)return null;n=fe(n,et.isCSP),o="script-src 'self' 'unsafe-eval' http: https: data: blob: mediastream: filesystem:";break;case"inline-font":if(e)return null;n=fe(n,et.isCSP),o="font-src 'self' 'unsafe-eval' http: https: data: blob: mediastream: filesystem:";break;case"replace":case"content":if(e||(0===h.length?!1===de(n,et.isException):null===dt(h)))return null;n=fe(n,et.isReplace),o=h;break;default:{let t=0;switch(l){case"all":if(e)return null;break;case"image":t=et.fromImage;break;case"media":t=et.fromMedia;break;case"object":case"object-subrequest":t=et.fromObject;break;case"other":t=et.fromOther;break;case"ping":case"beacon":t=et.fromPing;break;case"script":t=et.fromScript;break;case"css":case"stylesheet":t=et.fromStylesheet;break;case"frame":case"subdocument":t=et.fromSubdocument;break;case"xhr":case"xmlhttprequest":t=et.fromXmlHttpRequest;break;case"websocket":t=et.fromWebsocket;break;case"font":t=et.fromFont;break;case"doc":case"document":t=et.fromDocument;break;default:return null}e?c=pe(c,t):a=fe(a,t);break}}}}let d;if(n|=0===a?c:c===tt?a:a&c,h-l>=2&&47===e.charCodeAt(l)&&47===e.charCodeAt(h-1)){d=e.slice(l,h);try{ot(d,!1,!1,!0)}catch(e){return null}n=fe(n,et.isFullRegex)}else{if(h>0&&124===e.charCodeAt(h-1)&&(n=fe(n,et.isRightAnchor),h-=1),l<h&&124===e.charCodeAt(l)&&(l<h-1&&124===e.charCodeAt(l+1)?(n=fe(n,et.isHostnameAnchor),l+=2):(n=fe(n,et.isLeftAnchor),l+=1)),de(n,et.isHostnameAnchor)){let t=l;for(;t<h&&!0===(ve(f=e.charCodeAt(t))||ke(f)||95===f||45===f||46===f);)t+=1;if(t===h)s=e.slice(l,h),l=h;else{s=e.slice(l,t),l=t;const i=e.charCodeAt(t);94===i?h-l==1?(l=h,n=fe(n,et.isRightAnchor)):(n=fe(n,et.isRegex),n=fe(n,et.isLeftAnchor)):n=fe(n,42===i?et.isRegex:et.isLeftAnchor)}}h-l>0&&42===e.charCodeAt(h-1)&&(h-=1),!1===de(n,et.isHostnameAnchor)&&h-l>0&&42===e.charCodeAt(l)&&(n=pe(n,et.isLeftAnchor),l+=1),de(n,et.isLeftAnchor)&&(h-l==5&&be(e,"ws://",l)?(n=fe(n,et.fromWebsocket),n=pe(n,et.isLeftAnchor),n=pe(n,et.fromHttp),n=pe(n,et.fromHttps),l=h):h-l==7&&be(e,"http://",l)?(n=fe(n,et.fromHttp),n=pe(n,et.fromHttps),n=pe(n,et.isLeftAnchor),l=h):h-l==8&&be(e,"https://",l)?(n=fe(n,et.fromHttps),n=pe(n,et.fromHttp),n=pe(n,et.isLeftAnchor),l=h):h-l==8&&be(e,"http*://",l)&&(n=fe(n,et.fromHttps),n=fe(n,et.fromHttp),n=pe(n,et.isLeftAnchor),l=h)),h-l>0&&(d=e.slice(l,h).toLowerCase(),n=gt(n,et.isUnicode,Ce(d)),!1===de(n,et.isRegex)&&(n=gt(n,et.isRegex,function(e,t,s){const i=e.indexOf("^",t);if(-1!==i&&i<s)return!0;const r=e.indexOf("*",t);return-1!==r&&r<s}(d,0,d.length)))),void 0!==s&&(s=s.toLowerCase(),Ce(s)&&(n=gt(n,et.isUnicode,!0),s=O(s)))}var f;return new pt({filter:d,hostname:s,mask:n,domains:i,denyallow:r,optionValue:o,rawLine:!0===t?e:void 0,regex:void 0})}static deserialize(e){const t=e.getUint32(),s=e.getUint8(),i=de(t,et.isUnicode);return new pt({mask:t,filter:1&~s?void 0:i?e.getUTF8():e.getNetworkFilter(),hostname:2&~s?void 0:e.getNetworkHostname(),domains:4&~s?void 0:Pe.deserialize(e),rawLine:8&~s?void 0:e.getRawNetwork(),denyallow:16&~s?void 0:Pe.deserialize(e),optionValue:32&~s?void 0:de(t,et.isCSP)?e.getNetworkCSP():de(t,et.isRedirect)?e.getNetworkRedirect():e.getUTF8(),regex:void 0})}constructor({filter:e,hostname:t,mask:s,domains:i,denyallow:r,optionValue:o,rawLine:n,regex:a}){this.filter=e,this.hostname=t,this.mask=s,this.domains=i,this.denyallow=r,this.optionValue=o,this.rawLine=n,this.id=void 0,this.regex=a}get csp(){if(this.isCSP())return this.optionValue}get redirect(){if(this.isRedirect())return this.optionValue}isCosmeticFilter(){return!1}isNetworkFilter(){return!0}match(e){return function(e,t){if(!1===e.isCptAllowed(t.type)||!0===t.isHttps&&!1===e.fromHttps()||!0===t.isHttp&&!1===e.fromHttp()||!1===e.firstParty()&&!0===t.isFirstParty||!1===e.thirdParty()&&!0===t.isThirdParty)return!1;if(void 0!==e.domains&&!1===e.domains.match(t.sourceHostnameHashes,t.sourceEntityHashes))return!1;if(void 0!==e.denyallow&&!0===e.denyallow.match(t.getHostnameHashes(),t.getEntityHashes()))return!1;return!0}(this,e)&&function(e,t){const s=e.getFilter();if(!0===e.isHostnameAnchor()){const i=e.getHostname();if(!1===function(e,t,s){if(0===e.length)return!0;if(e.length>t.length)return!1;if(e.length===t.length)return e===t;const i=t.indexOf(e);if(-1===i)return!1;if(0===i)return!0===s||46===t.charCodeAt(e.length)||46===e.charCodeAt(e.length-1);if(t.length===i+e.length)return 46===t.charCodeAt(i-1)||46===e.charCodeAt(0);return!(!0!==s&&46!==t.charCodeAt(e.length)&&46!==e.charCodeAt(e.length-1)||46!==t.charCodeAt(i-1)&&46!==e.charCodeAt(0))}(i,t.hostname,void 0!==e.filter&&42===e.filter.charCodeAt(0)))return!1;if(e.isRegex())return e.getRegex().test(t.url.slice(t.url.indexOf(i)+i.length));if(e.isRightAnchor()&&e.isLeftAnchor()){return s===t.url.slice(t.url.indexOf(i)+i.length)}if(e.isRightAnchor()){const r=t.hostname;return!1===e.hasFilter()?i.length===r.length||r.endsWith(i):t.url.endsWith(s)}return e.isLeftAnchor()?be(t.url,s,t.url.indexOf(i)+i.length):!1===e.hasFilter()||-1!==t.url.indexOf(s,t.url.indexOf(i)+i.length)}if(e.isRegex())return e.getRegex().test(t.url);if(e.isLeftAnchor()&&e.isRightAnchor())return t.url===s;if(e.isLeftAnchor())return we(t.url,s);if(e.isRightAnchor())return t.url.endsWith(s);if(!1===e.hasFilter())return!0;return-1!==t.url.indexOf(s)}(this,e)}serialize(e){e.pushUint32(this.mask);const t=e.getPos();e.pushUint8(0);let s=0;void 0!==this.filter&&(s|=1,this.isUnicode()?e.pushUTF8(this.filter):e.pushNetworkFilter(this.filter)),void 0!==this.hostname&&(s|=2,e.pushNetworkHostname(this.hostname)),void 0!==this.domains&&(s|=4,this.domains.serialize(e)),void 0!==this.rawLine&&(s|=8,e.pushRawNetwork(this.rawLine)),void 0!==this.denyallow&&(s|=16,this.denyallow.serialize(e)),void 0!==this.optionValue&&(s|=32,this.isCSP()?e.pushNetworkCSP(this.optionValue):this.isRedirect()?e.pushNetworkRedirect(this.optionValue):e.pushUTF8(this.optionValue)),e.setByte(t,s)}getSerializedSize(e){let t=5;return void 0!==this.filter&&(!0===this.isUnicode()?t+=$(this.filter):t+=function(e,t){return!0===t?M(D().networkFilter.getCompressedSize(e),!1):_(e)}(this.filter,e)),void 0!==this.hostname&&(t+=function(e,t){return!0===t?M(D().networkHostname.getCompressedSize(e),!1):_(e)}(this.hostname,e)),void 0!==this.domains&&(t+=this.domains.getSerializedSize()),void 0!==this.rawLine&&(t+=function(e,t){return!0===t?M(D().networkRaw.getCompressedSize(E(e)),!1):$(e)}(this.rawLine,e)),void 0!==this.denyallow&&(t+=this.denyallow.getSerializedSize()),void 0!==this.optionValue&&(this.isCSP()?t+=function(e,t){return!0===t?M(D().networkCSP.getCompressedSize(e),!1):_(e)}(this.optionValue,e):this.isRedirect()?t+=function(e,t){return!0===t?M(D().networkRedirect.getCompressedSize(e),!1):_(e)}(this.optionValue,e):t+=$(this.optionValue)),t}toString(e){if(void 0!==this.rawLine)return this.rawLine;let t="";this.isException()&&(t+="@@"),this.isHostnameAnchor()?t+="||":this.fromHttp()!==this.fromHttps()?this.fromHttp()?t+="|http://":t+="|https://":this.isLeftAnchor()&&(t+="|"),this.hasHostname()&&(t+=this.getHostname(),t+="^"),this.isFullRegex()?t+=`/${this.getRegex().source}/`:this.isRegex()?t+=this.getRegex().source:t+=this.getFilter(),this.isRightAnchor()&&"^"!==t[t.length-1]&&(t+="|");const s=[];if(!1===this.fromAny()){const e=ue(this.getCptMask());if(ue(tt)-e<e)for(const e of function(e){const t=[];return!1===e.fromDocument()&&t.push("document"),!1===e.fromImage()&&t.push("image"),!1===e.fromMedia()&&t.push("media"),!1===e.fromObject()&&t.push("object"),!1===e.fromOther()&&t.push("other"),!1===e.fromPing()&&t.push("ping"),!1===e.fromScript()&&t.push("script"),!1===e.fromStylesheet()&&t.push("stylesheet"),!1===e.fromSubdocument()&&t.push("sub_frame"),!1===e.fromWebsocket()&&t.push("websocket"),!1===e.fromXmlHttpRequest()&&t.push("xhr"),!1===e.fromFont()&&t.push("font"),t}(this))s.push(`~${e}`);else for(const e of it(this))s.push(e)}return this.isImportant()&&s.push("important"),this.isRedirectRule()?""===this.optionValue?s.push("redirect-rule"):s.push(`redirect-rule=${this.optionValue}`):this.isRedirect()&&(""===this.optionValue?s.push("redirect"):s.push(`redirect=${this.optionValue}`)),this.isCSP()&&s.push(`csp=${this.optionValue}`),this.isElemHide()&&s.push("elemhide"),this.isSpecificHide()&&s.push("specifichide"),this.isGenericHide()&&s.push("generichide"),this.firstParty()!==this.thirdParty()&&(this.firstParty()&&s.push("1p"),this.thirdParty()&&s.push("3p")),void 0!==this.domains&&(void 0!==this.domains.parts?s.push(`domain=${this.domains.parts}`):s.push("domain=<hashed>")),void 0!==this.denyallow&&(void 0!==this.denyallow.parts?s.push(`denyallow=${this.denyallow.parts}`):s.push("denyallow=<hashed>")),this.isBadFilter()&&s.push("badfilter"),s.length>0&&(t+="function"==typeof e?`$${s.map(e).join(",")}`:`$${s.join(",")}`),t}getIdWithoutBadFilter(){return rt(this.mask&~et.isBadFilter,this.filter,this.hostname,this.domains,this.denyallow,this.optionValue)}getId(){return void 0===this.id&&(this.id=rt(this.mask,this.filter,this.hostname,this.domains,this.denyallow,this.optionValue)),this.id}hasFilter(){return void 0!==this.filter}hasDomains(){return void 0!==this.domains}getMask(){return this.mask}getCptMask(){return this.getMask()&tt}isRedirect(){return de(this.getMask(),et.isRedirect)}isRedirectRule(){return de(this.mask,et.isRedirectRule)}getRedirect(){var e;return null!==(e=this.optionValue)&&void 0!==e?e:""}isReplace(){return de(this.getMask(),et.isReplace)}getHtmlModifier(){var e;return 0===(null===(e=this.optionValue)||void 0===e?void 0:e.length)?null:dt(this.optionValue)}isHtmlFilteringRule(){return this.isReplace()}getRedirectResource(){const e=this.getRedirect(),t=e.lastIndexOf(":");return-1===t?e:e.slice(0,t)}getRedirectPriority(){const e=this.getRedirect(),t=e.lastIndexOf(":");return-1===t?0:Number(e.slice(t+1))}hasHostname(){return void 0!==this.hostname}getHostname(){return this.hostname||""}getFilter(){return this.filter||""}getRegex(){return void 0===this.regex&&(this.regex=void 0!==this.filter&&this.isRegex()?ot(this.filter,this.isLeftAnchor(),this.isRightAnchor(),this.isFullRegex()):ft),this.regex}getTokens(){if(ce.reset(),void 0!==this.domains&&void 0!==this.domains.hostnames&&void 0===this.domains.entities&&void 0===this.domains.notHostnames&&void 0===this.domains.notEntities&&1===this.domains.hostnames.length&&ce.push(this.domains.hostnames[0]),!1===this.isFullRegex()){if(void 0!==this.filter){const e=!this.isRightAnchor(),t=!this.isLeftAnchor();!function(e,t,s,i){const r=Math.min(e.length,2*i.remaining());let o=!1,n=0,a=0,c=he;for(let s=0;s<r;s+=1){const r=e.charCodeAt(s);!0===xe(r)?(!1===o&&(c=he,o=!0,a=s),c=c*le^r):(!0===o&&(o=!1,s-a>1&&42!==r&&42!==n&&(!1===t||0!==a)&&i.push(c>>>0)),n=r)}!1===s&&!0===o&&42!==n&&e.length-a>1&&!1===i.full()&&i.push(c>>>0)}(this.filter,t,e,ce)}void 0!==this.hostname&&Se(this.hostname,!1,void 0!==this.filter&&42===this.filter.charCodeAt(0),ce)}else void 0!==this.filter&&function(e,t){let s=e.length-1,i=1,r=0;for(;i<s;i+=1){const t=e.charCodeAt(i);if(124===t)return;if(40===t||42===t||43===t||63===t||91===t||123===t||46===t&&92!==r||92===t&&ke(e.charCodeAt(i+1)))break;r=t}for(r=0;s>=i;s-=1){const t=e.charCodeAt(s);if(124===t)return;if(41===t||42===t||43===t||63===t||93===t||125===t||46===t&&92!==e.charCodeAt(s-1)||92===t&&ke(r))break;r=t}if(s<i){const s=94!==e.charCodeAt(1),i=36!==e.charCodeAt(e.length-1);Se(e.slice(1,e.length-1),s,i,t)}else i>1&&Se(e.slice(1,i),94!==e.charCodeAt(1),!0,t),s<e.length-1&&Se(e.slice(s+1,e.length-1),!0,94!==e.charCodeAt(e.length-1),t)}(this.filter,ce);if(!0===ce.empty()&&void 0!==this.domains&&void 0!==this.domains.hostnames&&void 0===this.domains.entities&&void 0===this.domains.notHostnames&&void 0===this.domains.notEntities){const e=[];for(const t of this.domains.hostnames){const s=new Uint32Array(1);s[0]=t,e.push(s)}return e}if(!0===ce.empty()&&!1===this.fromAny()){const e=it(this);if(0!==e.length){const t=[];for(const s of e){const e=new Uint32Array(1);e[0]=Ue[s],t.push(e)}return t}}return!0===this.fromHttp()&&!1===this.fromHttps()?ce.push(Qe):!0===this.fromHttps()&&!1===this.fromHttp()&&ce.push(Je),[ce.slice()]}isCptAllowed(e){const t=st[e];return void 0!==t?de(this.mask,t):this.fromAny()}isException(){return de(this.mask,et.isException)}isHostnameAnchor(){return de(this.mask,et.isHostnameAnchor)}isRightAnchor(){return de(this.mask,et.isRightAnchor)}isLeftAnchor(){return de(this.mask,et.isLeftAnchor)}isImportant(){return de(this.mask,et.isImportant)}isFullRegex(){return de(this.mask,et.isFullRegex)}isRegex(){return de(this.mask,et.isRegex)||de(this.mask,et.isFullRegex)}isPlain(){return!this.isRegex()}isCSP(){return de(this.mask,et.isCSP)}isElemHide(){return this.isSpecificHide()&&this.isGenericHide()}isSpecificHide(){return de(this.mask,et.isSpecificHide)}isGenericHide(){return de(this.mask,et.isGenericHide)}isBadFilter(){return de(this.mask,et.isBadFilter)}isUnicode(){return de(this.mask,et.isUnicode)}fromAny(){return this.getCptMask()===tt}thirdParty(){return de(this.mask,et.thirdParty)}firstParty(){return de(this.mask,et.firstParty)}fromImage(){return de(this.mask,et.fromImage)}fromMedia(){return de(this.mask,et.fromMedia)}fromObject(){return de(this.mask,et.fromObject)}fromOther(){return de(this.mask,et.fromOther)}fromPing(){return de(this.mask,et.fromPing)}fromScript(){return de(this.mask,et.fromScript)}fromStylesheet(){return de(this.mask,et.fromStylesheet)}fromDocument(){return de(this.mask,et.fromDocument)}fromSubdocument(){return de(this.mask,et.fromSubdocument)}fromWebsocket(){return de(this.mask,et.fromWebsocket)}fromHttp(){return de(this.mask,et.fromHttp)}fromHttps(){return de(this.mask,et.fromHttps)}fromXmlHttpRequest(){return de(this.mask,et.fromXmlHttpRequest)}fromFont(){return de(this.mask,et.fromFont)}}function gt(e,t,s){return!0===s?fe(e,t):pe(e,t)}class mt extends Map{}var yt;function wt(e){return e.length<6||33!==e.charCodeAt(0)||35!==e.charCodeAt(1)?yt.INVALID:e.startsWith("!#if ")?yt.BEGIF:e.startsWith("!#else")?yt.ELSE:e.startsWith("!#endif")?yt.ENDIF:yt.INVALID}!function(e){e[e.INVALID=0]="INVALID",e[e.BEGIF=1]="BEGIF",e[e.ELSE=2]="ELSE",e[e.ENDIF=3]="ENDIF"}(yt||(yt={}));const bt=/(!|&&|\|\||\(|\)|[a-zA-Z0-9_]+)/g,vt=/^[a-zA-Z0-9_]+$/,kt={"!":2,"&&":1,"||":0},xt=e=>Object.prototype.hasOwnProperty.call(kt,e),St=(e,t)=>"true"===e&&!t.has("true")||!("false"===e&&!t.has("false"))&&!!t.get(e),Ft=(e,t)=>{if(0===e.length)return!1;if((e=>vt.test(e))(e))return"!"===e[0]?!St(e.slice(1),t):St(e,t);const s=(e=>e.match(bt))(e);if(!s||0===s.length)return!1;if(e.length!==s.reduce(((e,t)=>e+t.length),0))return!1;const i=[],r=[];for(const e of s)if("("===e)r.push(e);else if(")"===e){for(;0!==r.length&&"("!==r[r.length-1];)i.push(r.pop());if(0===r.length)return!1;r.pop()}else if(xt(e)){for(;r.length&&xt(r[r.length-1])&&kt[e]<=kt[r[r.length-1]];)i.push(r.pop());r.push(e)}else i.push(St(e,t));if("("===r[0]||")"===r[0])return!1;for(;0!==r.length;)i.push(r.pop());for(const e of i)if(!0===e||!1===e)r.push(e);else if("!"===e)r.push(!r.pop());else if(xt(e)){const t=r.pop(),s=r.pop();"&&"===e?r.push(s&&t):r.push(s||t)}return!0===r[0]};class At{static getCondition(e){return e.slice(5).replace(/\s/g,"")}static parse(e,t){return new this({condition:At.getCondition(e),filterIDs:t})}static deserialize(e){const t=e.getUTF8(),s=new Set;for(let t=0,i=e.getUint32();t<i;t++)s.add(e.getUint32());return new this({condition:t,filterIDs:s})}constructor({condition:e,filterIDs:t=new Set}){this.condition=e,this.filterIDs=t}evaluate(e){return Ft(this.condition,e)}serialize(e){e.pushUTF8(this.condition),e.pushUint32(this.filterIDs.size);for(const t of this.filterIDs)e.pushUint32(t)}getSerializedSize(){let e=$(this.condition);return e+=4*(1+this.filterIDs.size),e}}var zt;function It(t,{extendedNonSupportedTypes:s=!1}={}){if(0===t.length||1===t.length)return s?e.FilterType.NOT_SUPPORTED_EMPTY:e.FilterType.NOT_SUPPORTED;const i=t.charCodeAt(0),r=t.charCodeAt(1);if(33===i||35===i&&r<=32||91===i&&we(t,"[Adblock"))return s?e.FilterType.NOT_SUPPORTED_COMMENT:e.FilterType.NOT_SUPPORTED;const o=t.charCodeAt(t.length-1);if(36===i&&36!==r&&64!==r||38===i||42===i||45===i||46===i||47===i||58===i||61===i||63===i||64===i||95===i||124===i||124===o)return e.FilterType.NETWORK;const n=t.indexOf("$");if(-1!==n&&n!==t.length-1){const i=n+1,r=t.charCodeAt(i);if(36===r||64===r&&be(t,"@$",i))return s?e.FilterType.NOT_SUPPORTED_ADGUARD:e.FilterType.NOT_SUPPORTED}const a=t.indexOf("#");if(-1!==a&&a!==t.length-1){const i=a+1,r=t.charCodeAt(i);if(35===r||64===r&&be(t,"@#",i))return e.FilterType.COSMETIC;if(64===r&&(be(t,"@$#",i)||be(t,"@%#",i)||be(t,"@?#",i))||37===r&&be(t,"%#",i)||36===r&&(be(t,"$#",i)||be(t,"$?#",i))||63===r&&be(t,"?#",i))return s?e.FilterType.NOT_SUPPORTED_ADGUARD:e.FilterType.NOT_SUPPORTED}return e.FilterType.NETWORK}function Ct(t){const s=It(t);return s===e.FilterType.NETWORK?pt.parse(t,!0):s===e.FilterType.COSMETIC?Ye.parse(t,!0):null}function Rt(t,s=new W){s=new W(s);const i=[],r=[],o=[],n=t.split("\n"),a=[],c=[];for(let t=0;t<n.length;t+=1){let l=n[t];if(0!==l.length&&l.charCodeAt(0)<=32&&(l=l.trim()),l.length>2)for(;t<n.length-1&&92===l.charCodeAt(l.length-1)&&32===l.charCodeAt(l.length-2);){l=l.slice(0,-2);const e=n[t+1];if(!(e.length>4&&32===e.charCodeAt(0)&&32===e.charCodeAt(1)&&32===e.charCodeAt(2)&&32===e.charCodeAt(3)&&32!==e.charCodeAt(4)))break;l+=e.slice(4),t+=1}0!==l.length&&l.charCodeAt(l.length-1)<=32&&(l=l.trim());const h=It(l,{extendedNonSupportedTypes:!0});if(h===e.FilterType.NETWORK&&!0===s.loadNetworkFilters){const e=pt.parse(l,s.debug);null!==e?(i.push(e),c.length>0&&c[c.length-1].filterIDs.add(e.getId())):o.push({lineNumber:t,filter:l,filterType:h})}else if(h===e.FilterType.COSMETIC&&!0===s.loadCosmeticFilters){const i=Ye.parse(l,s.debug);null!==i?!0!==s.loadGenericCosmeticsFilters&&!1!==i.isGenericHide()||(r.push(i),c.length>0&&c[c.length-1].filterIDs.add(i.getId())):o.push({lineNumber:t,filter:l,filterType:e.FilterType.COSMETIC})}else if(s.loadPreprocessors){const s=wt(l);if(s===yt.BEGIF)c.length>0?c.push(new At({condition:`(${c[c.length-1].condition})&&(${At.getCondition(l)})`})):c.push(At.parse(l));else if((s===yt.ENDIF||s===yt.ELSE)&&c.length>0){const e=c.pop();a.push(e),s===yt.ELSE&&c.push(new At({condition:`!(${e.condition})`}))}else h===e.FilterType.NOT_SUPPORTED_ADGUARD&&o.push({lineNumber:t,filter:l,filterType:h})}else h===e.FilterType.NOT_SUPPORTED_ADGUARD&&o.push({lineNumber:t,filter:l,filterType:h})}return{networkFilters:i,cosmeticFilters:r,preprocessors:a.filter((e=>e.filterIDs.size>0)),notSupportedFilters:o}}function Ut(e,t){const{networkFilters:s,cosmeticFilters:i,preprocessors:r}=Rt(e,t);return{filters:[].concat(s).concat(i),preprocessors:r}}function Tt(e){if(null===e)return!1;if("object"!=typeof e)return!1;const{name:t,aliases:s,body:i,contentType:r}=e;return"string"==typeof t&&(!(!Array.isArray(s)||!s.every((e=>"string"==typeof e)))&&("string"==typeof i&&"string"==typeof r))}function Et(e){if(null===e)return!1;if("object"!=typeof e)return!1;const{name:t,aliases:s,body:i,dependencies:r,executionWorld:o,requiresTrust:n}=e;return"string"==typeof t&&(!(!Array.isArray(s)||!s.every((e=>"string"==typeof e)))&&("string"==typeof i&&(!(!Array.isArray(r)||!r.every((e=>"string"==typeof e)))&&((void 0===o||"MAIN"===o||"ISOLATED"===o)&&(void 0===n||"boolean"==typeof n)))))}e.FilterType=void 0,(zt=e.FilterType||(e.FilterType={}))[zt.NOT_SUPPORTED=0]="NOT_SUPPORTED",zt[zt.NETWORK=1]="NETWORK",zt[zt.COSMETIC=2]="COSMETIC",zt[zt.NOT_SUPPORTED_EMPTY=100]="NOT_SUPPORTED_EMPTY",zt[zt.NOT_SUPPORTED_COMMENT=101]="NOT_SUPPORTED_COMMENT",zt[zt.NOT_SUPPORTED_ADGUARD=102]="NOT_SUPPORTED_ADGUARD";class Ot{static deserialize(e){const t=e.getASCII(),s=[],i=[];for(let t=0,i=e.getUint16();t<i;t++){const t=e.getASCII(),i=[];for(let t=0,s=e.getUint16();t<s;t++)i.push(e.getASCII());s.push({name:t,aliases:i,body:e.getUTF8(),contentType:e.getASCII()})}for(let t=0,s=e.getUint16();t<s;t++){const t=e.getASCII(),s=[];for(let t=0,i=e.getUint16();t<i;t++)s.push(e.getASCII());const r=e.getUTF8(),o=e.getBool(),n=e.getBool(),a=e.getBool(),c=e.getBool(),l=[];for(let t=0,s=e.getUint16();t<s;t++)l.push(e.getASCII());const h={name:t,aliases:s,body:r,dependencies:l};o&&(h.executionWorld=!0===n?"ISOLATED":"MAIN"),a&&(h.requiresTrust=c),i.push(h)}return new Ot({checksum:t,scriptlets:i,resources:s})}static parse(e,{checksum:t}){const s=JSON.parse(e);if(null===s||"object"!=typeof s)throw new Error("Cannot parse resources.json");const{scriptlets:i,redirects:r}=s,o=[];if(Array.isArray(r))for(const e of r){if(!Tt(e))throw new Error(`Cannot parse redirect resource: ${JSON.stringify(e)}`);o.push(e)}const n=[];if(Array.isArray(i))for(const e of i){if(!Et(e))throw new Error(`Cannot parse scriptlet: ${JSON.stringify(e)}`);n.push(e)}return new Ot({checksum:t,scriptlets:n,resources:o})}static copy(e){const t=e.checksum,s=[],i=[];for(const t of e.resources)s.push(structuredClone(t));for(const t of e.scriptlets)i.push(structuredClone(t));return new this({checksum:t,resources:s,scriptlets:i})}constructor({checksum:e="",resources:t=[],scriptlets:s=[]}={}){this.checksum=e,this.resources=t,this.scriptlets=s,this.scriptletsCache=new Map,this.resourcesByName=new Map,this.scriptletsByName=new Map,this.updateAliases()}updateAliases(){this.scriptletsCache.clear(),this.resourcesByName.clear(),this.scriptletsByName.clear();for(const e of this.resources)for(const t of[e.name,...e.aliases]){if(this.resourcesByName.has(t))throw new Error(`Resource with a name or alias "${t}" already exists`);this.resourcesByName.set(t,e)}for(const e of this.scriptlets)for(const t of[e.name,...e.aliases]){if(this.scriptletsByName.has(t))throw new Error(`Scriptlet with a name or alias "${t}" already exists`);this.scriptletsByName.set(t,e)}for(const e of this.scriptlets)for(const t of e.dependencies)if(!this.scriptletsByName.has(t))throw new Error(`Scriptlet with a name or alias "${e.name}" has a missing depencency "${t}"`)}getResource(e){const{body:t,contentType:s}=this.resourcesByName.get(e)||o.getResourceForMime(e);let i;var r;return i=-1!==s.indexOf(";")?`data:${s},${t}`:`data:${s};base64,${r=t,"undefined"!=typeof btoa?btoa(r):"undefined"!=typeof Buffer?Buffer.from(r).toString("base64"):r}`,{body:t,contentType:s,dataUrl:i}}getScriptlet(e){if(e.endsWith(".fn"))return;const t=this.scriptletsByName.get(e.endsWith(".js")?e:`${e}.js`);if(void 0===t)return;let s=this.scriptletsCache.get(t.name);if(void 0!==s){if(0===s.length)return;return s}const i=this.getScriptletDependencies(t);return s=((e,t=[])=>["if (typeof scriptletGlobals === 'undefined') { var scriptletGlobals = {}; }",...t,`(${e})(...['{{1}}','{{2}}','{{3}}','{{4}}','{{5}}','{{6}}','{{7}}','{{8}}','{{9}}','{{10}}'].filter((a,i) => a !== '{{'+(i+1)+'}}').map((a) => decodeURIComponent(a)))`].join(";"))(t.body,i),this.scriptletsCache.set(t.name,s),s}getScriptletDependencies(e){const t=new Map,s=[...e.dependencies];for(;s.length>0;){const e=s.pop();if(t.has(e))continue;const i=this.scriptletsByName.get(e);t.set(e,i.body),s.push(...i.dependencies)}return Array.from(t.values())}getSerializedSize(){let e=_(this.checksum);e+=2;for(const{name:t,aliases:s,body:i,contentType:r}of this.resources)e+=_(t),e+=s.reduce(((e,t)=>e+_(t)),2),e+=$(i),e+=_(r);e+=2;for(const{name:t,aliases:s,body:i,dependencies:r}of this.scriptlets)e+=_(t),e+=s.reduce(((e,t)=>e+_(t)),2),e+=$(i),e+=1,e+=1,e+=1,e+=1,e+=r.reduce(((e,t)=>e+_(t)),2);return e}serialize(e){e.pushASCII(this.checksum),e.pushUint16(this.resources.length);for(const{name:t,aliases:s,body:i,contentType:r}of this.resources){e.pushASCII(t),e.pushUint16(s.length);for(const t of s)e.pushASCII(t);e.pushUTF8(i),e.pushASCII(r)}e.pushUint16(this.scriptlets.length);for(const{name:t,aliases:s,body:i,dependencies:r,executionWorld:o,requiresTrust:n}of this.scriptlets){e.pushASCII(t),e.pushUint16(s.length);for(const t of s)e.pushASCII(t);e.pushUTF8(i),e.pushBool(void 0!==o),e.pushBool("ISOLATED"===o),e.pushBool(void 0!==n),e.pushBool(!0===n),e.pushUint16(r.length),r.forEach((t=>e.pushASCII(t)))}}}const Ht=new Uint32Array(0);function jt(e){return`(?:${e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")})`}function Pt(e,t,s){let i=e.get(t);void 0===i&&(i=[],e.set(t,i)),i.push(s)}function Lt(e,t){const s=new Map;for(const i of e)Pt(s,t(i),i);return Array.from(s.values())}function Dt(e,t){const s=[],i=[];for(const r of e)t(r)?s.push(r):i.push(r);return{negative:i,positive:s}}const Bt=[{description:"Remove duplicated filters by ID",fusion:e=>e[0],groupByCriteria:e=>""+e.getId(),select:()=>!0},{description:"Group idential filter with same mask but different domains in single filters",fusion:e=>{const t=[],s=new Set,i=new Set,r=new Set,o=new Set;for(const{domains:n}of e)if(void 0!==n){if(void 0!==n.parts&&t.push(n.parts),void 0!==n.hostnames)for(const e of n.hostnames)s.add(e);if(void 0!==n.entities)for(const e of n.entities)r.add(e);if(void 0!==n.notHostnames)for(const e of n.notHostnames)i.add(e);if(void 0!==n.notEntities)for(const e of n.notEntities)o.add(e)}return new pt(Object.assign({},e[0],{domains:new Pe({hostnames:0!==s.size?new Uint32Array(s).sort():void 0,entities:0!==r.size?new Uint32Array(r).sort():void 0,notHostnames:0!==i.size?new Uint32Array(i).sort():void 0,notEntities:0!==o.size?new Uint32Array(o).sort():void 0,parts:0!==t.length?t.join(","):void 0}),rawLine:void 0!==e[0].rawLine?e.map((({rawLine:e})=>e)).join(" <+> "):void 0}))},groupByCriteria:e=>{var t;return e.getHostname()+e.getFilter()+e.getMask()+(null!==(t=e.optionValue)&&void 0!==t?t:"")},select:e=>!e.isCSP()&&void 0===e.denyallow&&void 0!==e.domains},{description:"Group simple patterns, into a single filter",fusion:e=>{const t=[];for(const s of e)s.isRegex()?t.push(`(?:${s.getRegex().source})`):s.isRightAnchor()?t.push(`${jt(s.getFilter())}$`):s.isLeftAnchor()?t.push(`^${jt(s.getFilter())}`):t.push(jt(s.getFilter()));return new pt(Object.assign({},e[0],{mask:fe(e[0].mask,et.isRegex),rawLine:void 0!==e[0].rawLine?e.map((({rawLine:e})=>e)).join(" <+> "):void 0,regex:new RegExp(t.join("|"))}))},groupByCriteria:e=>""+(e.getMask()&~et.isRegex&~et.isFullRegex),select:e=>void 0===e.domains&&void 0===e.denyallow&&!e.isHostnameAnchor()&&!e.isRedirect()&&!e.isCSP()}];function Nt(e){return e}function Mt(e){return e}function _t(e){const t=[];let s=e;for(const{select:e,fusion:i,groupByCriteria:r}of Bt){const{positive:o,negative:n}=Dt(s,e);s=n;const a=Lt(o,r);for(const e of a)e.length>1?t.push(i(e)):s.push(e[0])}for(const e of s)t.push(e);return t}function $t(e){return e--,e|=e>>1,e|=e>>2,e|=e>>4,e|=e>>8,e|=e>>16,++e}let qt=1;const Vt=Number.MAX_SAFE_INTEGER>>>0;class Wt{static deserialize(e,t,s,i){const r=e.getUint32(),o=e.getUint32(),n=e.getUint32(),a=V.fromUint8Array(e.getBytes(!0),i),c=a.getUint32ArrayView(r),l=a.getUint32ArrayView(o),h=a.pos;return a.seekZero(),new Wt({config:i,deserialize:t,filters:[],optimize:s}).updateInternals({bucketsIndex:l,filtersIndexStart:h,numberOfFilters:n,tokensLookupIndex:c,view:a})}constructor({deserialize:e,filters:t,optimize:s,config:i}){this.bucketsIndex=j,this.filtersIndexStart=0,this.numberOfFilters=0,this.tokensLookupIndex=j,this.cache=new Map,this.view=V.empty(i),this.deserializeFilter=e,this.optimize=s,this.config=i,0!==t.length&&this.update(t,void 0)}getFilters(){const e=[];if(0===this.numberOfFilters)return e;this.view.setPos(this.filtersIndexStart);for(let t=0;t<this.numberOfFilters;t+=1)e.push(this.deserializeFilter(this.view));return this.view.seekZero(),e}getTokens(){const e=new Set;for(let t=0;t<this.bucketsIndex.length;t+=2)e.add(this.bucketsIndex[t]);return new Uint32Array(e)}getSerializedSize(){return 12+N(this.view.buffer,!0)}serialize(e){e.pushUint32(this.tokensLookupIndex.length),e.pushUint32(this.bucketsIndex.length),e.pushUint32(this.numberOfFilters),e.pushBytes(this.view.buffer,!0)}iterMatchingFilters(e,t){const s=function(){const e=qt;return qt=(qt+1)%1e9,e}();for(const i of e)if(!1===this.iterBucket(i,s,t))return;this.iterBucket(0,s,t)}update(e,t){0!==this.cache.size&&this.cache.clear();const s=this.config.enableCompression;let i=0,r=0;const o=[];let n=0,a=this.view.buffer.byteLength-this.filtersIndexStart,c=this.getFilters();if(0!==c.length){void 0!==t&&0!==t.size&&(c=c.filter((e=>!t.has(e.getId())||(a-=e.getSerializedSize(s),!1))));for(const t of e)a+=t.getSerializedSize(s),c.push(t)}else{c=e;for(const t of e)a+=t.getSerializedSize(s)}if(0===c.length)return void this.updateInternals({bucketsIndex:j,filtersIndexStart:0,numberOfFilters:0,tokensLookupIndex:j,view:V.empty(this.config)});!0===this.config.debug&&c.sort(((e,t)=>e.getId()-t.getId()));const l=new Uint32Array(Math.max($t(2*c.length),256));for(const e of c){const t=e.getTokens();o.push(t),n+=2*t.length,r+=t.length;for(const e of t){i+=e.length;for(const t of e)l[t%l.length]+=1}}a+=4*n;const h=Math.max(2,$t(r)),u=h-1,d=[];for(let e=0;e<h;e+=1)d.push([]);a+=4*h;const f=V.allocate(a,this.config),p=f.getUint32ArrayView(h),g=f.getUint32ArrayView(n),m=f.getPos();for(let e=0;e<o.length;e+=1){const t=c[e],s=o[e],r=f.pos;t.serialize(f);for(const e of s){let t=0,s=i+1;for(const i of e){const e=l[i%l.length];if(e<s&&(s=e,t=i,1===s))break}d[t&u].push([t,r])}}let y=0;for(let e=0;e<h;e+=1){const t=d[e];p[e]=y;for(const[e,s]of t)g[y++]=e,g[y++]=s}f.seekZero(),this.updateInternals({bucketsIndex:g,filtersIndexStart:m,numberOfFilters:o.length,tokensLookupIndex:p,view:f})}updateInternals({bucketsIndex:e,filtersIndexStart:t,numberOfFilters:s,tokensLookupIndex:i,view:r}){return this.bucketsIndex=e,this.filtersIndexStart=t,this.numberOfFilters=s,this.tokensLookupIndex=i,this.view=r,r.seekZero(),this}iterBucket(e,t,s){let i=!0===this.config.enableInMemoryCache?this.cache.get(e):void 0;if(void 0===i){const t=e&this.tokensLookupIndex.length-1,s=this.tokensLookupIndex[t];if(s===Vt)return!0;const r=t===this.tokensLookupIndex.length-1?this.bucketsIndex.length:this.tokensLookupIndex[t+1],o=[];for(let t=s;t<r;t+=2){this.bucketsIndex[t]===e&&o.push(this.bucketsIndex[t+1])}if(0===o.length)return!0;const n=[],a=this.view;for(let e=0;e<o.length;e+=1)a.setPos(o[e]),n.push(this.deserializeFilter(a));i={filters:n.length>1?this.optimize(n):n,lastRequestSeen:-1},!0===this.config.enableInMemoryCache&&this.cache.set(e,i)}if(i.lastRequestSeen!==t){i.lastRequestSeen=t;const e=i.filters;for(let t=0;t<e.length;t+=1)if(!1===s(e[t])){if(t>0){const s=e[t];e[t]=e[t-1],e[t-1]=s}return!1}}return!0}}const Gt=new Uint8Array(4);class Xt{static deserialize(e,t,s){const i=new Xt({deserialize:t,config:s,filters:[]});return i.filters=e.getBytes(),i}constructor({config:e,deserialize:t,filters:s}){this.deserialize=t,this.filters=Gt,this.config=e,0!==s.length&&this.update(s,void 0)}update(e,t){let s=this.filters.byteLength,i=[];const r=this.config.enableCompression,o=this.getFilters();if(0!==o.length)if(void 0===t||0===t.size)i=o;else for(const e of o)!1===t.has(e.getId())?i.push(e):s-=e.getSerializedSize(r);const n=i.length!==o.length,a=i.length;for(const t of e)s+=t.getSerializedSize(r),i.push(t);const c=i.length>a;if(0===i.length)this.filters=Gt;else if(!0===c||!0===n){const e=V.allocate(s,this.config);e.pushUint32(i.length),!0===this.config.debug&&i.sort(((e,t)=>e.getId()-t.getId()));for(const t of i)t.serialize(e);this.filters=e.buffer}}getSerializedSize(){return N(this.filters,!1)}serialize(e){e.pushBytes(this.filters)}getFilters(){if(this.filters.byteLength<=4)return[];const e=[],t=V.fromUint8Array(this.filters,this.config),s=t.getUint32();for(let i=0;i<s;i+=1)e.push(this.deserialize(t));return e}}function Kt(e,t){if(0===e.length)return"";const s=1024,i=[],r=` { ${t} }`;for(let t=0;t<e.length;t+=s){let o=e[t];for(let i=t+1,r=Math.min(e.length,t+s);i<r;i+=1)o+=",\n"+e[i];if(o+=r,e.length<s)return o;i.push(o)}return i.join("\n")}function Zt(e,t=$e){const s=new Map;for(const i of e){const e=i.getStyle(t),r=s.get(e);void 0===r?s.set(e,[i.getSelector()]):r.push(i.getSelector())}const i=[],r=Array.from(s.entries());for(const[e,t]of r)i.push(Kt(t,e));return i.join("\n\n")}function Yt(e,t=$e){const s=[];for(const i of e){if(i.hasCustomStyle())return Zt(e,t);s.push(i.selector)}return Kt(s,t)}function Qt(e,t){const s=He(e,t),i=Oe(e,t),r=new Uint32Array(s.length+i.length);let o=0;for(const e of s)r[o++]=e;for(const e of i)r[o++]=e;return r}class Jt{static deserialize(e,t){const s=new Jt({config:t});return s.genericRules=Xt.deserialize(e,Ye.deserialize,t),s.classesIndex=Wt.deserialize(e,Ye.deserialize,Mt,t),s.hostnameIndex=Wt.deserialize(e,Ye.deserialize,Mt,t),s.hrefsIndex=Wt.deserialize(e,Ye.deserialize,Mt,t),s.idsIndex=Wt.deserialize(e,Ye.deserialize,Mt,t),s.unhideIndex=Wt.deserialize(e,Ye.deserialize,Mt,t),s}constructor({filters:e=[],config:t}){this.genericRules=new Xt({config:t,deserialize:Ye.deserialize,filters:[]}),this.classesIndex=new Wt({config:t,deserialize:Ye.deserialize,filters:[],optimize:Mt}),this.hostnameIndex=new Wt({config:t,deserialize:Ye.deserialize,filters:[],optimize:Mt}),this.hrefsIndex=new Wt({config:t,deserialize:Ye.deserialize,filters:[],optimize:Mt}),this.idsIndex=new Wt({config:t,deserialize:Ye.deserialize,filters:[],optimize:Mt}),this.unhideIndex=new Wt({config:t,deserialize:Ye.deserialize,filters:[],optimize:Mt}),this.baseStylesheet=null,this.extraGenericRules=null,0!==e.length&&this.update(e,void 0,t)}getFilters(){return[].concat(this.genericRules.getFilters(),this.classesIndex.getFilters(),this.hostnameIndex.getFilters(),this.hrefsIndex.getFilters(),this.idsIndex.getFilters(),this.unhideIndex.getFilters())}update(e,t,s){const i=[],r=[],o=[],n=[],a=[],c=[];for(const t of e)t.isUnhide()?c.push(t):t.isGenericHide()?t.isClassSelector()?i.push(t):t.isIdSelector()?a.push(t):t.isHrefSelector()?n.push(t):r.push(t):!1!==t.isExtended()&&!0!==s.loadExtendedSelectors||o.push(t);this.genericRules.update(r,t),this.classesIndex.update(i,t),this.hostnameIndex.update(o,t),this.hrefsIndex.update(n,t),this.idsIndex.update(a,t),this.unhideIndex.update(c,t)}getSerializedSize(){return this.genericRules.getSerializedSize()+this.classesIndex.getSerializedSize()+this.hostnameIndex.getSerializedSize()+this.hrefsIndex.getSerializedSize()+this.idsIndex.getSerializedSize()+this.unhideIndex.getSerializedSize()}serialize(e){this.genericRules.serialize(e),this.classesIndex.serialize(e),this.hostnameIndex.serialize(e),this.hrefsIndex.serialize(e),this.idsIndex.serialize(e),this.unhideIndex.serialize(e)}getCosmeticsFilters({domain:e,hostname:t,classes:s=[],hrefs:i=[],ids:r=[],allowGenericHides:o=!0,allowSpecificHides:n=!0,getRulesFromDOM:a=!0,getRulesFromHostname:c=!0,hidingStyle:l=$e,isFilterExcluded:h}){const u=Qt(t,e),d=[];if(!0===c&&this.hostnameIndex.iterMatchingFilters(u,(s=>(!0!==n&&!0!==s.isScriptInject()||!s.match(t,e)||(null==h?void 0:h(s))||d.push(s),!0))),!0===o&&!0===c){const s=this.getGenericRules(l);for(const i of s)!0!==i.match(t,e)||(null==h?void 0:h(i))||d.push(i)}!0===o&&!0===a&&0!==s.length&&this.classesIndex.iterMatchingFilters(ye(s),(s=>(s.match(t,e)&&!(null==h?void 0:h(s))&&d.push(s),!0))),!0===o&&!0===a&&0!==r.length&&this.idsIndex.iterMatchingFilters(ye(r),(s=>(s.match(t,e)&&!(null==h?void 0:h(s))&&d.push(s),!0))),!0===o&&!0===a&&0!==i.length&&this.hrefsIndex.iterMatchingFilters(function(e){const t=e.sort();let s=1;for(let e=1;e<t.length;e+=1)t[s-1]!==t[e]&&(t[s++]=t[e]);return t.subarray(0,s)}(function(e){if(0===e.length)return Ht;if(1===e.length)return e[0];let t=0;for(let s=0;s<e.length;s+=1)t+=e[s].length;const s=new Uint32Array(t);let i=0;for(let t=0;t<e.length;t+=1){const r=e[t];for(let e=0;e<r.length;e+=1)s[i++]=r[e]}return s}(i.map((e=>Ae(e))))),(s=>(s.match(t,e)&&!(null==h?void 0:h(s))&&d.push(s),!0)));const f=[];return 0!==d.length&&this.unhideIndex.iterMatchingFilters(u,(s=>(s.match(t,e)&&!(null==h?void 0:h(s))&&f.push(s),!0))),{filters:d,unhides:f}}getStylesheetsFromFilters({filters:e,extendedFilters:t},{getBaseRules:s,allowGenericHides:i,hidingStyle:r=$e}){let o=!1===s||!1===i?"":this.getBaseStylesheet(r);0!==e.length&&(0!==o.length&&(o+="\n\n"),o+=Yt(e,r));const n=[];if(0!==t.length){const e=new Map;for(const s of t){const t=s.getSelectorAST();if(void 0!==t){const i=s.isRemove()?void 0:s.getStyleAttributeHash();void 0!==i&&e.set(s.getStyle(r),i),n.push({ast:t,remove:s.isRemove(),attribute:i})}}0!==e.size&&(0!==o.length&&(o+="\n\n"),o+=[...e.entries()].map((([e,t])=>`[${t}] { ${e} }`)).join("\n\n"))}return{stylesheet:o,extended:n}}getGenericRules(e){return null===this.extraGenericRules?this.lazyPopulateGenericRulesCache(e).genericRules:this.extraGenericRules}getBaseStylesheet(e){return null===this.baseStylesheet?this.lazyPopulateGenericRulesCache(e).baseStylesheet:this.baseStylesheet}lazyPopulateGenericRulesCache(e){if(null===this.baseStylesheet||null===this.extraGenericRules){const t=this.unhideIndex.getFilters(),s=new Set;for(const e of t)s.add(e.getSelector());const i=this.genericRules.getFilters(),r=[],o=[];for(const e of i)e.hasCustomStyle()||e.isScriptInject()||e.hasHostnameConstraint()||s.has(e.getSelector())?o.push(e):r.push(e);this.baseStylesheet=Yt(r,e),this.extraGenericRules=o}return{baseStylesheet:this.baseStylesheet,genericRules:this.extraGenericRules}}}class es{static deserialize(e,t){const s=new es({config:t});return s.index=Wt.deserialize(e,pt.deserialize,t.enableOptimizations?_t:Nt,t),s.badFilters=Xt.deserialize(e,pt.deserialize,t),s}constructor({filters:e=[],config:t}){this.index=new Wt({config:t,deserialize:pt.deserialize,filters:[],optimize:t.enableOptimizations?_t:Nt}),this.badFiltersIds=null,this.badFilters=new Xt({config:t,deserialize:pt.deserialize,filters:[]}),0!==e.length&&this.update(e,void 0)}getFilters(){return[].concat(this.badFilters.getFilters(),this.index.getFilters())}update(e,t){const s=[],i=[];for(const t of e)t.isBadFilter()?s.push(t):i.push(t);this.badFilters.update(s,t),this.index.update(i,t),this.badFiltersIds=null}getSerializedSize(){return this.badFilters.getSerializedSize()+this.index.getSerializedSize()}serialize(e){this.index.serialize(e),this.badFilters.serialize(e)}matchAll(e,t){const s=[];return this.index.iterMatchingFilters(e.getTokens(),(i=>(i.match(e)&&!1===this.isFilterDisabled(i)&&!(null==t?void 0:t(i))&&s.push(i),!0))),s}match(e,t){let s;return this.index.iterMatchingFilters(e.getTokens(),(i=>!(i.match(e)&&!1===this.isFilterDisabled(i)&&!(null==t?void 0:t(i)))||(s=i,!1))),s}isFilterDisabled(e){if(null===this.badFiltersIds){const e=this.badFilters.getFilters();if(0===e.length)return!1;const t=new Set;for(const s of e)t.add(s.getIdWithoutBadFilter());this.badFiltersIds=t}return this.badFiltersIds.has(e.getId())}}class ts{static deserialize(e,t){const s=new ts({config:t});return s.networkIndex=Wt.deserialize(e,pt.deserialize,t.enableOptimizations?_t:Nt,t),s.exceptionsIndex=Wt.deserialize(e,pt.deserialize,t.enableOptimizations?_t:Nt,t),s.cosmeticIndex=Wt.deserialize(e,Ye.deserialize,Mt,t),s.unhideIndex=Wt.deserialize(e,Ye.deserialize,Mt,t),s}constructor({filters:e=[],config:t}){this.config=t,this.networkIndex=new Wt({config:t,deserialize:pt.deserialize,filters:[],optimize:t.enableOptimizations?_t:Nt}),this.exceptionsIndex=new Wt({config:t,deserialize:pt.deserialize,filters:[],optimize:t.enableOptimizations?_t:Nt}),this.cosmeticIndex=new Wt({config:t,deserialize:Ye.deserialize,filters:[],optimize:Mt}),this.unhideIndex=new Wt({config:t,deserialize:Ye.deserialize,filters:[],optimize:Mt}),0!==e.length&&this.update(e,void 0)}update(e,t){const s=[],i=[],r=[],o=[];for(const t of e)t.isNetworkFilter()?t.isException()?i.push(t):s.push(t):t.isCosmeticFilter()&&(t.isUnhide()?o.push(t):r.push(t));this.networkIndex.update(s,t),this.exceptionsIndex.update(i,t),this.cosmeticIndex.update(r,t),this.unhideIndex.update(o,t)}serialize(e){this.networkIndex.serialize(e),this.exceptionsIndex.serialize(e),this.cosmeticIndex.serialize(e),this.unhideIndex.serialize(e)}getSerializedSize(){return this.networkIndex.getSerializedSize()+this.exceptionsIndex.getSerializedSize()+this.cosmeticIndex.getSerializedSize()+this.unhideIndex.getSerializedSize()}getHTMLFilters(e,t){const s=[],i=[],r=[],o=[];if(!0===this.config.loadNetworkFilters&&this.networkIndex.iterMatchingFilters(e.getTokens(),(i=>(i.match(e)&&!(null==t?void 0:t(i))&&s.push(i),!0))),0!==s.length&&this.exceptionsIndex.iterMatchingFilters(e.getTokens(),(s=>(s.match(e)&&!(null==t?void 0:t(s))&&r.push(s),!0))),!0===this.config.loadCosmeticFilters&&e.isMainFrame()){const{hostname:s,domain:r=""}=e,n=Qt(s,r);this.cosmeticIndex.iterMatchingFilters(n,(e=>(e.match(s,r)&&!(null==t?void 0:t(e))&&i.push(e),!0))),0!==i.length&&this.unhideIndex.iterMatchingFilters(n,(e=>(e.match(s,r)&&!(null==t?void 0:t(e))&&o.push(e),!0)))}return{networkFilters:s,cosmeticFilters:i,unhides:o,exceptions:r}}getFilters(){return[].concat(this.networkIndex.getFilters(),this.exceptionsIndex.getFilters(),this.cosmeticIndex.getFilters(),this.unhideIndex.getFilters())}}const ss=Number.MAX_SAFE_INTEGER>>>0;class is{static deserialize(e,t){const s=e.getUint32(),i=e.getUint32(),r=e.getUint32(),o=V.fromUint8Array(e.getBytes(!0),{enableCompression:!1}),n=o.getUint32ArrayView(s),a=o.getUint32ArrayView(i),c=o.pos;return o.seekZero(),new is({deserialize:t,values:[],getKeys:()=>[],getSerializedSize:()=>0,serialize:()=>{}}).updateInternals({bucketsIndex:a,valuesIndexStart:c,numberOfValues:r,tokensLookupIndex:n,view:o})}constructor({serialize:e,deserialize:t,getKeys:s,getSerializedSize:i,values:r}){if(this.cache=new Map,this.bucketsIndex=j,this.tokensLookupIndex=j,this.valuesIndexStart=0,this.numberOfValues=0,this.view=V.empty({enableCompression:!1}),this.deserializeValue=t,0!==r.length){const t=[];let o=0,n=0;for(const e of r)n+=i(e);if(0===r.length)return void this.updateInternals({bucketsIndex:j,valuesIndexStart:0,numberOfValues:0,tokensLookupIndex:j,view:V.empty({enableCompression:!1})});for(const e of r){const i=s(e);t.push(i),o+=2*i.length}n+=4*o;const a=Math.max(2,$t(r.length)),c=a-1,l=[];for(let e=0;e<a;e+=1)l.push([]);n+=4*a;const h=V.allocate(n,{enableCompression:!1}),u=h.getUint32ArrayView(a),d=h.getUint32ArrayView(o),f=h.getPos();for(let s=0;s<t.length;s+=1){const i=r[s],o=t[s],n=h.pos;e(i,h);for(const e of o)l[e&c].push([e,n])}let p=0;for(let e=0;e<a;e+=1){const t=l[e];u[e]=p;for(const[e,s]of t)d[p++]=e,d[p++]=s}this.updateInternals({bucketsIndex:d,valuesIndexStart:f,numberOfValues:t.length,tokensLookupIndex:u,view:h})}}updateInternals({bucketsIndex:e,valuesIndexStart:t,numberOfValues:s,tokensLookupIndex:i,view:r}){return this.bucketsIndex=e,this.valuesIndexStart=t,this.numberOfValues=s,this.tokensLookupIndex=i,this.view=r,r.seekZero(),this}getValues(){const e=[];if(0===this.numberOfValues)return e;this.view.setPos(this.valuesIndexStart);for(let t=0;t<this.numberOfValues;t+=1)e.push(this.deserializeValue(this.view));return this.view.seekZero(),e}getSerializedSize(){return 12+N(this.view.buffer,!0)}serialize(e){e.pushUint32(this.tokensLookupIndex.length),e.pushUint32(this.bucketsIndex.length),e.pushUint32(this.numberOfValues),e.pushBytes(this.view.buffer,!0)}get(e){const t=this.cache.get(e);if(void 0!==t)return t;const s=e&this.tokensLookupIndex.length-1,i=this.tokensLookupIndex[s];if(i===ss)return[];const r=s===this.tokensLookupIndex.length-1?this.bucketsIndex.length:this.tokensLookupIndex[s+1],o=[];for(let t=i;t<r;t+=2){this.bucketsIndex[t]===e&&o.push(this.bucketsIndex[t+1])}if(0===o.length)return[];const n=[],a=this.view;for(let e=0;e<o.length;e+=1)a.setPos(o[e]),n.push(this.deserializeValue(a));return this.cache.set(e,n),n}}function rs(e){if(null===e)return!1;if("object"!=typeof e)return!1;const{key:t,name:s,color:i,description:r}=e;return"string"==typeof t&&("string"==typeof s&&("string"==typeof i&&"string"==typeof r))}function os(e){return me(e.key)}function ns(e){return $(e.key)+$(e.name)+$(e.color)+$(e.description)}function as(e,t){t.pushUTF8(e.key),t.pushUTF8(e.name),t.pushUTF8(e.color),t.pushUTF8(e.description)}function cs(e){return{key:e.getUTF8(),name:e.getUTF8(),color:e.getUTF8(),description:e.getUTF8()}}function ls(e){return new is({getSerializedSize:ns,getKeys:e=>[os(e)],serialize:as,deserialize:cs,values:e})}function hs(e){if(null===e)return!1;if("object"!=typeof e)return!1;const{key:t,name:s,description:i,country:r,website_url:o,privacy_policy_url:n,privacy_contact:a,ghostery_id:c}=e;return"string"==typeof t&&("string"==typeof s&&((null===i||"string"==typeof i)&&((null===r||"string"==typeof r)&&((null===o||"string"==typeof o)&&((null===n||"string"==typeof n)&&((null===a||"string"==typeof a)&&(null===c||"string"==typeof c)))))))}function us(e){return me(e.key)}function ds(e){return $(e.key)+$(e.name)+$(e.description||"")+$(e.website_url||"")+$(e.country||"")+$(e.privacy_policy_url||"")+$(e.privacy_contact||"")+$(e.ghostery_id||"")}function fs(e,t){t.pushUTF8(e.key),t.pushUTF8(e.name),t.pushUTF8(e.description||""),t.pushUTF8(e.website_url||""),t.pushUTF8(e.country||""),t.pushUTF8(e.privacy_policy_url||""),t.pushUTF8(e.privacy_contact||""),t.pushUTF8(e.ghostery_id||"")}function ps(e){return{key:e.getUTF8(),name:e.getUTF8(),description:e.getUTF8()||null,website_url:e.getUTF8()||null,country:e.getUTF8()||null,privacy_policy_url:e.getUTF8()||null,privacy_contact:e.getUTF8()||null,ghostery_id:e.getUTF8()||null}}function gs(e){return new is({getSerializedSize:ds,getKeys:e=>[us(e)],serialize:fs,deserialize:ps,values:e})}function ms(e){if(null===e)return!1;if("object"!=typeof e)return!1;const{key:t,name:s,category:i,organization:r,alias:o,website_url:n,domains:a,filters:c}=e;return"string"==typeof t&&("string"==typeof s&&("string"==typeof i&&((null===r||"string"==typeof r)&&(("string"==typeof o||null===o)&&((null===n||"string"==typeof n)&&(!(!Array.isArray(a)||!a.every((e=>"string"==typeof e)))&&!(!Array.isArray(c)||!c.every((e=>"string"==typeof e)))))))))}function ys(e){const t=[];for(const s of e.filters){const e=pt.parse(s);null!==e&&t.push(e.getId())}for(const s of e.domains){const e=pt.parse(`||${s}^`);null!==e&&t.push(e.getId())}return[...new Set(t)]}function ws(e){let t=B(e.domains.length);for(const s of e.domains)t+=$(s);let s=B(e.filters.length);for(const t of e.filters)s+=$(t);return $(e.key)+$(e.name)+$(e.category)+$(e.organization||"")+$(e.alias||"")+$(e.website_url||"")+$(e.ghostery_id||"")+t+s}function bs(e,t){t.pushUTF8(e.key),t.pushUTF8(e.name),t.pushUTF8(e.category),t.pushUTF8(e.organization||""),t.pushUTF8(e.alias||""),t.pushUTF8(e.website_url||""),t.pushUTF8(e.ghostery_id||""),t.pushLength(e.domains.length);for(const s of e.domains)t.pushUTF8(s);t.pushLength(e.filters.length);for(const s of e.filters)t.pushUTF8(s)}function vs(e){const t=e.getUTF8(),s=e.getUTF8(),i=e.getUTF8(),r=e.getUTF8()||null,o=e.getUTF8()||null,n=e.getUTF8()||null,a=e.getUTF8()||null,c=e.getLength(),l=[];for(let t=0;t<c;t+=1)l.push(e.getUTF8());const h=e.getLength(),u=[];for(let t=0;t<h;t+=1)u.push(e.getUTF8());return{key:t,name:s,category:i,organization:r,alias:o,website_url:n,ghostery_id:a,domains:l,filters:u}}function ks(e){return new is({getSerializedSize:ws,getKeys:ys,serialize:bs,deserialize:vs,values:e})}class xs{static deserialize(e){const t=new xs(null);return t.categories=is.deserialize(e,cs),t.organizations=is.deserialize(e,ps),t.patterns=is.deserialize(e,vs),t}constructor(e){if(!e)return this.organizations=gs([]),this.categories=ls([]),void(this.patterns=ks([]));const{patterns:t,organizations:s,categories:i}=e,r=[];if("object"==typeof i)for(const[e,t]of Object.entries(i)){if("object"!=typeof t)continue;const s={key:e,...t};rs(s)?r.push(s):console.error("?? invalid category",s)}this.categories=ls(r);const o=[];if("object"==typeof s)for(const[e,t]of Object.entries(s)){if("object"!=typeof t)continue;const s={key:e,...t};hs(s)?o.push(s):console.error("?? invalid organization",s)}this.organizations=gs(o);const n=[];if("object"==typeof t)for(const[e,s]of Object.entries(t)){if("object"!=typeof s)continue;const t={key:e,...s};ms(t)?n.push(t):console.error("?? invalid pattern",t)}this.patterns=ks(n)}getCategories(){return this.categories.getValues()}getOrganizations(){return this.organizations.getValues()}getPatterns(){return this.patterns.getValues()}getSerializedSize(){return this.categories.getSerializedSize()+this.organizations.getSerializedSize()+this.patterns.getSerializedSize()}serialize(e){this.categories.serialize(e),this.organizations.serialize(e),this.patterns.serialize(e)}fromFilter(e){return this.fromId(e.getId())}fromDomain(e){const t=e.split(".");for(;t.length>=2;t.shift()){const e=t.join("."),s=pt.parse(`||${e}^`);if(null===s)continue;const i=this.fromId(s.getId());if(i.length>0)return i}return[]}fromId(e){var t,s;const i=[];for(const r of this.patterns.get(e))i.push({pattern:r,category:null===(t=this.categories.get(os({key:r.category})))||void 0===t?void 0:t[0],organization:null!==r.organization?null===(s=this.organizations.get(us({key:r.organization})))||void 0===s?void 0:s[0]:null});return i}}class Ss{static deserialize(e){const t=new Set;for(let s=0,i=e.getUint32();s<i;s++)t.add(e.getUint32());const s=[];for(let t=0,i=e.getUint32();t<i;t++)s.push(At.deserialize(e));return new this({excluded:t,preprocessors:s})}constructor({excluded:e=new Set,preprocessors:t=[]}){this.excluded=e,this.preprocessors=t}isFilterExcluded(e){return this.excluded.has(e.getId())}updateEnv(e){this.excluded.clear();for(const t of this.preprocessors)if(!t.evaluate(e))for(const e of t.filterIDs)this.excluded.add(e)}update({added:e,removed:t},s){if(t)for(const e of t){const t=this.preprocessors.find((t=>t.condition===e.condition));if(t)for(const s of e.filterIDs)t.filterIDs.delete(s)}if(e)for(const t of e){const e=this.preprocessors.find((e=>e.condition===t.condition));if(e)for(const s of t.filterIDs)e.filterIDs.add(s);else this.preprocessors.push(t)}(t&&0!==t.length||e&&0!==e.length)&&this.updateEnv(s)}serialize(e){e.pushUint32(this.excluded.size);for(const t of this.excluded)e.pushUint32(t);e.pushUint32(this.preprocessors.length);for(const t of this.preprocessors)t.serialize(e)}getSerializedSize(){let e=4*(1+this.excluded.size);e+=4;for(const t of this.preprocessors)e+=t.getSerializedSize();return e}}const Fs=696;function As(e){if(0===e.length)return!1;let t,s=0;for(const i of e){const e=(i.isImportant()?4:0)|(i.isException()?1:2);e>=s&&(s=e,t=i)}return void 0!==t&&t.isException()}const zs=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,8,8,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,10,3,3,3,3,3,3,3,3,3,3,3,3,4,3,3,11,6,6,6,5,8,8,8,8,8,8,8,8,8,8,8,0,1,2,3,5,8,7,1,1,1,4,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,2,1,1,1,1,1,2,1,2,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,1,2,1,1,1,1,1,1,1,1,1,1,1,1,1,3,1,3,1,1,1,1,1,1,1,3,1,1,1,1,1,3,1,3,1,1,1,1,1,1,1,3,1,1,1,1,1,1,1,1,1,1,1,1,1,1]);e.Config=W,e.CosmeticFilter=Ye,e.ENGINE_VERSION=Fs,e.FiltersEngine=class extends Y{static fromCached(e,t){if(void 0===t)return e();const{path:s,read:i,write:r}=t;return i(s).then((e=>this.deserialize(e))).catch((()=>e().then((e=>r(s,e.serialize()).then((()=>e))))))}static empty(e={}){return new this({config:e})}static fromLists(e,t,s={},i){return this.fromCached((()=>{const i=re(e,t),r=oe(e);return Promise.all([i,r]).then((([e,t])=>{const i=this.parse(e.join("\n"),s);return void 0!==t&&i.updateResources(t,""+t.length),i}))}),i)}static fromPrebuiltAdsOnly(e=fetch,t){return this.fromLists(e,te,{},t)}static fromPrebuiltAdsAndTracking(e=fetch,t){return this.fromLists(e,se,{},t)}static fromPrebuiltFull(e=fetch,t){return this.fromLists(e,ie,{},t)}static fromTrackerDB(e,t={}){const s=new W(t),i=new xs(e),r=[];for(const e of i.getPatterns())r.push(...e.filters);const o=this.parse(r.join("\n"),s);return o.metadata=i,o}static merge(e,{skipResources:t=!1}={}){if(!e||e.length<2)throw new Error("merging engines requires at least two engines");const s=e[0].config,i=new Map,r=new Map,o=new Map,n=[],a={organizations:{},categories:{},patterns:{}},c=[],l=Object.keys(s).filter((function(e){return"boolean"==typeof s[e]&&!c.includes(e)}));for(const t of e){for(const e of l)if(s[e]!==t.config[e])throw new Error(`config "${e}" of all merged engines must be the same`);const e=t.getFilters();for(const t of e.networkFilters)r.set(t.getId(),t);for(const t of e.cosmeticFilters)o.set(t.getId(),t);for(const e of t.preprocessors.preprocessors)n.push(e);for(const[e,s]of t.lists)i.has(e)||i.set(e,s);if(void 0!==t.metadata){for(const e of t.metadata.organizations.getValues())void 0===a.organizations[e.key]&&(a.organizations[e.key]=e);for(const e of t.metadata.categories.getValues())void 0===a.categories[e.key]&&(a.categories[e.key]=e);for(const e of t.metadata.patterns.getValues())void 0===a.patterns[e.key]&&(a.patterns[e.key]=e)}}const h=new this({networkFilters:Array.from(r.values()),cosmeticFilters:Array.from(o.values()),preprocessors:n,lists:i,config:s});if(Object.keys(a.categories).length+Object.keys(a.organizations).length+Object.keys(a.patterns).length!==0&&(h.metadata=new xs(a)),!0!==t){for(const t of e.slice(1))if(t.resources.checksum!==e[0].resources.checksum)throw new Error(`resource checksum of all merged engines must match with the first one: "${e[0].resources.checksum}" but got: "${t.resources.checksum}"`);h.resources=Ot.copy(e[0].resources)}return h}static parse(e,t={}){const s=new W(t);return new this({...Rt(e,s),config:s})}static deserialize(e){const t=V.fromUint8Array(e,{enableCompression:!1}),s=t.getUint16();if(Fs!==s)throw new Error(`serialized engine version mismatch, expected 696 but got ${s}`);const i=W.deserialize(t);if(i.enableCompression&&t.enableCompression(),i.integrityCheck){const s=t.pos;t.pos=e.length-4;const i=t.checksum(),r=t.getUint32();if(i!==r)throw new Error(`serialized engine checksum mismatch, expected ${r} but got ${i}`);t.pos=s}const r=new this({config:i});r.resources=Ot.deserialize(t);const o=new Map,n=t.getUint16();for(let e=0;e<n;e+=1)o.set(t.getASCII(),t.getASCII());r.lists=o,r.preprocessors=Ss.deserialize(t),r.importants=es.deserialize(t,i),r.redirects=es.deserialize(t,i),r.filters=es.deserialize(t,i),r.exceptions=es.deserialize(t,i),r.csp=es.deserialize(t,i),r.cosmetics=Jt.deserialize(t,i),r.hideExceptions=es.deserialize(t,i),r.htmlFilters=ts.deserialize(t,i);return t.getBool()&&(r.metadata=xs.deserialize(t)),t.seekZero(),r}constructor({cosmeticFilters:e=[],networkFilters:t=[],preprocessors:s=[],config:i=new W,lists:r=new Map}={}){super(),this.config=new W(i),this.lists=r,this.preprocessors=new Ss({}),this.csp=new es({config:this.config}),this.hideExceptions=new es({config:this.config}),this.exceptions=new es({config:this.config}),this.importants=new es({config:this.config}),this.redirects=new es({config:this.config}),this.filters=new es({config:this.config}),this.cosmetics=new Jt({config:this.config}),this.htmlFilters=new ts({config:this.config}),this.resources=new Ot,0===t.length&&0===e.length||this.update({newCosmeticFilters:e,newNetworkFilters:t,newPreprocessors:s})}isFilterExcluded(e){return this.preprocessors.isFilterExcluded(e)}updateEnv(e){this.preprocessors.updateEnv(e)}getSerializedSize(){let e=1+this.config.getSerializedSize()+this.resources.getSerializedSize()+this.preprocessors.getSerializedSize()+this.filters.getSerializedSize()+this.exceptions.getSerializedSize()+this.importants.getSerializedSize()+this.redirects.getSerializedSize()+this.csp.getSerializedSize()+this.cosmetics.getSerializedSize()+this.hideExceptions.getSerializedSize()+this.htmlFilters.getSerializedSize()+4;for(const[t,s]of this.lists)e+=_(t)+_(s);return e+=1,void 0!==this.metadata&&(e+=this.metadata.getSerializedSize()),e}serialize(e){const t=V.fromUint8Array(e||new Uint8Array(this.getSerializedSize()),this.config);t.pushUint16(Fs),this.config.serialize(t),this.resources.serialize(t),t.pushUint16(this.lists.size);for(const[e,s]of Array.from(this.lists.entries()).sort())t.pushASCII(e),t.pushASCII(s);return this.preprocessors.serialize(t),this.importants.serialize(t),this.redirects.serialize(t),this.filters.serialize(t),this.exceptions.serialize(t),this.csp.serialize(t),this.cosmetics.serialize(t),this.hideExceptions.serialize(t),this.htmlFilters.serialize(t),t.pushBool(void 0!==this.metadata),void 0!==this.metadata&&this.metadata.serialize(t),this.config.integrityCheck&&t.pushUint32(t.checksum()),t.subarray()}loadedLists(){return Array.from(this.lists.keys())}hasList(e,t){return this.lists.has(e)&&this.lists.get(e)===t}updateResources(e,t){return this.resources.checksum!==t&&(this.resources=Ot.parse(e,{checksum:t}),!0)}getFilters(){const e=this.cosmetics.getFilters(),t=[...this.filters.getFilters(),...this.exceptions.getFilters(),...this.importants.getFilters(),...this.redirects.getFilters(),...this.csp.getFilters(),...this.hideExceptions.getFilters()];for(const s of this.htmlFilters.getFilters())s.isNetworkFilter()?t.push(s):s.isCosmeticFilter()&&e.push(s);return{cosmeticFilters:e,networkFilters:t}}update({newNetworkFilters:e=[],newCosmeticFilters:t=[],newPreprocessors:s=[],removedCosmeticFilters:i=[],removedNetworkFilters:r=[],removedPreprocessors:o=[]},n=new mt){let a=!1;!this.config.loadPreprocessors||0===s.length&&0===o.length||(a=!0,this.preprocessors.update({added:s,removed:o},n));const c=[];if(this.config.loadCosmeticFilters&&(0!==t.length||0!==i.length)){a=!0;const e=[];for(const s of t)s.isHtmlFiltering()?c.push(s):e.push(s);this.cosmetics.update(e,0===i.length?void 0:new Set(i),this.config)}if(this.config.loadNetworkFilters&&(0!==e.length||0!==r.length)){a=!0;const t=[],s=[],i=[],o=[],n=[],l=[];for(const r of e)r.isCSP()?s.push(r):r.isHtmlFilteringRule()?c.push(r):r.isGenericHide()||r.isSpecificHide()?l.push(r):r.isException()?i.push(r):r.isImportant()?o.push(r):r.isRedirect()?n.push(r):t.push(r);const h=0===r.length?void 0:new Set(r);this.importants.update(o,h),this.redirects.update(n,h),this.filters.update(t,h),!0===this.config.loadExceptionFilters&&this.exceptions.update(i,h),!0===this.config.loadCSPFilters&&this.csp.update(s,h),this.hideExceptions.update(l,h)}if(this.config.enableHtmlFiltering&&(0!==c.length||0!==r.length||0!==i.length)){const e=new Set([...r,...i]);this.htmlFilters.update(c,e)}return a}updateFromDiff({added:e,removed:t,preprocessors:s},i){const r=[],o=[],n=[],a=[],c=[],l=[];if(void 0!==t&&0!==t.length){const{networkFilters:e,cosmeticFilters:s}=Rt(t.join("\n"),this.config);Array.prototype.push.apply(a,s),Array.prototype.push.apply(c,e)}if(void 0!==e&&0!==e.length){const{networkFilters:t,cosmeticFilters:s}=Rt(e.join("\n"),this.config);Array.prototype.push.apply(r,s),Array.prototype.push.apply(o,t)}if(void 0!==s)for(const[e,t]of Object.entries(s)){if(void 0!==t.removed&&0!==t.removed.length){const{networkFilters:s,cosmeticFilters:i}=Rt(t.removed.join("\n"),this.config),r=new Set([].concat(i.map((e=>e.getId()))).concat(s.map((e=>e.getId()))));l.push(new At({condition:e,filterIDs:r}))}if(void 0!==t.added&&0!==t.added.length){const{networkFilters:s,cosmeticFilters:i}=Rt(t.added.join("\n"),this.config),r=new Set([].concat(i.map((e=>e.getId()))).concat(s.map((e=>e.getId()))));n.push(new At({condition:e,filterIDs:r}))}}return this.update({newCosmeticFilters:r,newNetworkFilters:o,newPreprocessors:n,removedCosmeticFilters:a.map((e=>e.getId())),removedNetworkFilters:c.map((e=>e.getId())),removedPreprocessors:l},i)}getHtmlFilters(t){const s=[];if(!1===this.config.enableHtmlFiltering)return s;const{networkFilters:i,exceptions:r,cosmeticFilters:o,unhides:n}=this.htmlFilters.getHTMLFilters(t,this.isFilterExcluded.bind(this));if(0!==o.length){const i=new Map(n.map((e=>[e.getSelector(),e])));for(const r of o){const o=r.getExtendedSelector();if(void 0===o)continue;const n=i.get(r.getSelector());void 0===n&&s.push(o),this.emit("filter-matched",{filter:r,exception:n},{request:t,filterType:e.FilterType.COSMETIC})}}if(0!==i.length){const o=new Map;let n;for(const e of r){const t=e.optionValue;if(""===t){n=e;break}o.set(t,e)}for(const r of i){const i=r.getHtmlModifier();if(null===i)continue;const a=n||o.get(r.optionValue);this.emit("filter-matched",{filter:r,exception:a},{request:t,filterType:e.FilterType.NETWORK}),void 0===a&&s.push(["replace",i])}}return 0!==s.length&&this.emit("html-filtered",s,t.url),s}getCosmeticsFilters({url:t,hostname:s,domain:i,classes:r,hrefs:o,ids:n,getBaseRules:a=!0,getInjectionRules:c=!0,getExtendedRules:l=!0,getRulesFromDOM:h=!0,getRulesFromHostname:u=!0,hidingStyle:d,callerContext:f}){if(!1===this.config.loadCosmeticFilters)return{active:!1,extended:[],scripts:[],styles:""};i||(i="");let p=!0,g=!0;const m=this.hideExceptions.matchAll(je.fromRawDetails({domain:i,hostname:s,url:t,sourceDomain:"",sourceHostname:"",sourceUrl:""}),this.isFilterExcluded.bind(this)),y=[],w=[];for(const e of m){if(e.isElemHide()){p=!1,g=!1;break}e.isSpecificHide()?w.push(e):e.isGenericHide()&&y.push(e)}!0===p&&(p=!1===As(y)),!0===g&&(g=!1===As(w));const{filters:b,unhides:v}=this.cosmetics.getCosmeticsFilters({domain:i,hostname:s,classes:r,hrefs:o,ids:n,allowGenericHides:p,allowSpecificHides:g,getRulesFromDOM:h,getRulesFromHostname:u,hidingStyle:d,isFilterExcluded:this.isFilterExcluded.bind(this)});let k=!1;const x=new Map;for(const e of v)!0===e.isScriptInject()&&!0===e.isUnhide()&&0===e.getSelector().length&&(k=!0),x.set(e.getSelector(),e);const S=[],F=[],A=[];if(0!==b.length)for(const s of b){const i=x.get(s.getSelector());if(void 0!==i)continue;let r=!1;!0===s.isScriptInject()?!0===c&&!1===k&&(S.push(s),r=!0):s.isExtended()?!0===l&&(A.push(s),r=!0):(F.push(s),r=!0),r&&this.emit("filter-matched",{filter:s,exception:i},{url:t,callerContext:f,filterType:e.FilterType.COSMETIC})}const z=[];for(const e of S){const s=e.getScript(this.resources.getScriptlet.bind(this.resources));void 0!==s&&(this.emit("script-injected",s,t),z.push(s))}const{stylesheet:I,extended:C}=this.cosmetics.getStylesheetsFromFilters({filters:F,extendedFilters:A},{getBaseRules:a,allowGenericHides:p,hidingStyle:d});return 0!==I.length&&this.emit("style-injected",I,t),{active:!0,extended:C,scripts:z,styles:I}}matchAll(e){const t=[];return e.isSupported&&(Array.prototype.push.apply(t,this.importants.matchAll(e,this.isFilterExcluded.bind(this))),Array.prototype.push.apply(t,this.filters.matchAll(e,this.isFilterExcluded.bind(this))),Array.prototype.push.apply(t,this.exceptions.matchAll(e,this.isFilterExcluded.bind(this))),Array.prototype.push.apply(t,this.csp.matchAll(e,this.isFilterExcluded.bind(this))),Array.prototype.push.apply(t,this.hideExceptions.matchAll(e,this.isFilterExcluded.bind(this))),Array.prototype.push.apply(t,this.redirects.matchAll(e,this.isFilterExcluded.bind(this)))),new Set(t)}getCSPDirectives(t){if(!this.config.loadNetworkFilters)return;if(!0!==t.isSupported||!1===t.isMainFrame())return;const s=this.csp.matchAll(t,this.isFilterExcluded.bind(this));if(0===s.length)return;const i=new Map,r=[];for(const o of s)if(o.isException()){if(void 0===o.csp)return void this.emit("filter-matched",{exception:o},{request:t,filterType:e.FilterType.NETWORK});i.set(o.csp,o)}else r.push(o);if(0===r.length)return;const o=new Set;for(const s of r.values()){const r=i.get(s.csp);void 0===r&&o.add(s.csp),this.emit("filter-matched",{filter:s,exception:r},{request:t,filterType:e.FilterType.NETWORK})}const n=Array.from(o).join("; ");return n.length>0&&this.emit("csp-injected",t,n),n}match(t,s=!1){const i={exception:void 0,filter:void 0,match:!1,redirect:void 0,metadata:void 0};if(!this.config.loadNetworkFilters)return i;if(t.isSupported){let e,s;if(i.filter=this.importants.match(t,this.isFilterExcluded.bind(this)),void 0===i.filter){const r=this.redirects.matchAll(t,this.isFilterExcluded.bind(this)).sort(((e,t)=>t.getRedirectPriority()-e.getRedirectPriority()));if(0!==r.length)for(const t of r)"none"===t.getRedirectResource()?e=t:t.isRedirectRule()?void 0===s&&(s=t):void 0===i.filter&&(i.filter=t);void 0===i.filter&&(i.filter=this.filters.match(t,this.isFilterExcluded.bind(this)),void 0!==s&&void 0!==i.filter&&(i.filter=s)),void 0!==i.filter&&(i.exception=this.exceptions.match(t,this.isFilterExcluded.bind(this)))}void 0!==i.filter&&void 0===i.exception&&i.filter.isRedirect()&&(void 0!==e?i.exception=e:i.redirect=this.resources.getResource(i.filter.getRedirectResource()))}return i.match=void 0===i.exception&&void 0!==i.filter,i.filter&&this.emit("filter-matched",{filter:i.filter,exception:i.exception},{request:t,filterType:e.FilterType.NETWORK}),void 0!==i.exception?this.emit("request-whitelisted",t,i):void 0!==i.redirect?this.emit("request-redirected",t,i):void 0!==i.filter?this.emit("request-blocked",t,i):this.emit("request-allowed",t,i),!0===s&&void 0!==i.filter&&this.metadata&&(i.metadata=this.metadata.fromFilter(i.filter)),i}getPatternMetadata(e,{getDomainMetadata:t=!1}={}){if(void 0===this.metadata)return[];const s=new Set,i=[];for(const t of this.matchAll(e))for(const e of this.metadata.fromFilter(t))s.has(e.pattern.key)||(s.add(e.pattern.key),i.push(e));if(t)for(const t of this.metadata.fromDomain(e.hostname))s.has(t.pattern.key)||(s.add(t.pattern.key),i.push(t));return i}blockScripts(){return this.updateFromDiff({added:[ae().scripts().redirectTo("javascript").toString()]}),this}blockImages(){return this.updateFromDiff({added:[ae().images().redirectTo("png").toString()]}),this}blockMedias(){return this.updateFromDiff({added:[ae().medias().redirectTo("mp4").toString()]}),this}blockFrames(){return this.updateFromDiff({added:[ae().frames().redirectTo("html").toString()]}),this}blockFonts(){return this.updateFromDiff({added:[ae().fonts().toString()]}),this}blockStyles(){return this.updateFromDiff({added:[ae().styles().toString()]}),this}},e.NetworkFilter=pt,e.Request=je,e.Resources=Ot,e.ReverseIndex=Wt,e.StreamingHtmlFilter=class{constructor(e){this.buffer="";const t=[],s=[];for(const i of e)"replace"===i[0]?t.push(i[1]):"script"===i[0]&&s.push(i);this.patterns=function(e){const t=[];for(const[s,i]of e){if("script"!==s)continue;const e=[],r=[];for(const t of i)47===t.charCodeAt(0)?t.endsWith("/")?r.push(new RegExp(t.slice(1,-1))):t.endsWith("/i")&&r.push(new RegExp(t.slice(1,-2),"i")):e.push(t);0===e.length&&0===r.length||t.push([e,r])}return t}(s),this.modifiers=t}flush(e=!0){let t=this.buffer;if(!0===e&&0!==this.modifiers.length){if(0!==this.patterns.length){const[e,s,i]=De(this.buffer,"script");t=Me(s,Ne(this.patterns,e))+i}t=function(e,t){if(0===t.length)return e;for(const[s,i]of t)e=e.replace(s,i);return e}(t,this.modifiers)}return this.buffer="",t}write(e){if(0===e.length)return e;if(0!==this.modifiers.length)return this.buffer+=e,"";if(0===this.patterns.length)return e;this.buffer+=e;const[t,s,i]=De(this.buffer,"script");return this.buffer=i,0===t.length?s:Me(s,Ne(this.patterns,t))}},e.adsAndTrackingLists=se,e.adsLists=te,e.detectFilterType=It,e.f=function(e){return Ct(e[0])},e.fetchLists=re,e.fetchResources=oe,e.fetchWithRetry=Q,e.fullLists=ie,e.generateDiff=function(e,t,s=new W){const i=new W(Object.assign({},s,{debug:!0})),r=Ut(e,i),o=new Set(r.filters.map((e=>e.getId()))),n=Ut(t,i),a=new Set(n.filters.map((e=>e.getId()))),c=new Set;for(const e of n.filters)o.has(e.getId())||c.add(e.rawLine);const l=new Set;for(const e of r.filters)a.has(e.getId())||l.add(e.rawLine);if(!s.loadPreprocessors)return{added:Array.from(c),removed:Array.from(l),preprocessors:{}};const h=new Map;for(const e of n.filters)h.set(e.getId(),e.rawLine);for(const e of r.filters)h.set(e.getId(),e.rawLine);const u={};for(const e of r.preprocessors){const t=n.preprocessors.find((t=>t.condition===e.condition));if(!t){const t=new Set;for(const s of e.filterIDs)t.add(h.get(s));u[e.condition]={added:[],removed:Array.from(t)};continue}const s={added:new Set,removed:new Set};for(const i of e.filterIDs)t.filterIDs.has(i)||s.removed.add(h.get(i));for(const i of t.filterIDs)e.filterIDs.has(i)||s.added.add(h.get(i));u[e.condition]={added:Array.from(s.added),removed:Array.from(s.removed)}}for(const e of n.preprocessors)if(!u[e.condition]){const t=new Set;for(const s of e.filterIDs)t.add(h.get(s));u[e.condition]={added:Array.from(t),removed:[]}}for(const[e,{added:t,removed:s}]of Object.entries(u))0===t.length&&0===s.length&&delete u[e];return{added:Array.from(c),removed:Array.from(l),preprocessors:u}},e.getHostnameHashesFromLabelsBackward=He,e.getLinesWithFilters=function(e,t=new W){return new Set(Ut(e,new W(Object.assign({},t,{debug:!0}))).filters.map((({rawLine:e})=>e)))},e.hasUnicode=Ce,e.isUTF8=function(e){if(0===e.length)return!0;if(!0===function(e){if(0===e.length)return!0;for(let t=0;t<e.length;t+=1)if(e[t]>127)return!1;return!0}(e))return!0;let t=0;for(let s=0;s<e.length;s+=1){const i=zs[e[s]];if(t=zs[256+16*t+i],1===t||void 0===t)return!1}return!0},e.makeRequest=function(e){return je.fromRawDetails(e)},e.mergeDiffs=function(e){const t=new Set,s=new Set,i={};for(const{added:r,removed:o,preprocessors:n}of e){if(void 0!==r)for(const e of r)s.has(e)&&s.delete(e),t.add(e);if(void 0!==o)for(const e of o)t.has(e)&&t.delete(e),s.add(e);if(n)for(const[e,t]of Object.entries(n))if(i[e]){if(void 0!==t.added)for(const s of t.added)i[e].removed.has(s)&&i[e].removed.delete(s),i[e].added.add(s);if(void 0!==t.removed)for(const s of t.removed)i[e].added.has(s)&&i[e].added.delete(s),i[e].removed.add(s)}else i[e]={added:void 0!==t.added?new Set(t.added):new Set,removed:void 0!==t.removed?new Set(t.removed):new Set}}return{added:Array.from(t),removed:Array.from(s),preprocessors:Object.fromEntries(Object.entries(i).map((([e,t])=>[e,{added:Array.from(t.added),removed:Array.from(t.removed)}])))}},e.parseFilter=Ct,e.parseFilters=Rt,e.tokenize=Ae}));
//# sourceMappingURL=adblocker.umd.min.js.map
