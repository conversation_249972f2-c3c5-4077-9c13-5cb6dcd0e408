/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
/* eslint-disable prettier/prettier */
export default [
    "securepubads.g.doubleclick",
    ".actonservice.com",
    "googlesyndication",
    "imasdk.googleapis",
    ".cloudfront.net",
    "analytics.",
    "marketing.",
    "tracking.",
    "metrics.",
    "images.",
    ".co.jp",
    ".co.uk",
    "a8clk.",
    "stats.",
    "a8cv.",
    "track",
    ".com",
    ".net",
    ".xyz",
    "ight",
    "tion",
    "www.",
    ".de",
    ".io",
    ".jp",
    "app",
    "cdn",
    "new",
    "web",
    ".b",
    ".c",
    ".d",
    ".f",
    ".h",
    ".j",
    ".k",
    ".l",
    ".m",
    ".n",
    ".p",
    ".s",
    ".t",
    ".v",
    ".w",
    "24",
    "a-",
    "a1",
    "a2",
    "a4",
    "a8",
    "ab",
    "ac",
    "ad",
    "af",
    "ag",
    "ah",
    "ai",
    "ak",
    "al",
    "am",
    "an",
    "ap",
    "ar",
    "as",
    "at",
    "au",
    "av",
    "aw",
    "ax",
    "ay",
    "az",
    "be",
    "bl",
    "bo",
    "br",
    "bu",
    "ca",
    "ce",
    "ch",
    "ck",
    "cl",
    "cr",
    "ct",
    "cu",
    "de",
    "di",
    "dn",
    "do",
    "dr",
    "ds",
    "e-",
    "eb",
    "ec",
    "ed",
    "ef",
    "eg",
    "el",
    "em",
    "en",
    "ep",
    "er",
    "es",
    "et",
    "eu",
    "ev",
    "ew",
    "ex",
    "ey",
    "fi",
    "fl",
    "fo",
    "fr",
    "ge",
    "gh",
    "gl",
    "go",
    "gr",
    "gs",
    "gu",
    "he",
    "ho",
    "ia",
    "ib",
    "ic",
    "id",
    "ie",
    "if",
    "ig",
    "ik",
    "il",
    "im",
    "in",
    "ip",
    "ir",
    "is",
    "it",
    "iv",
    "ix",
    "iz",
    "ks",
    "ld",
    "le",
    "li",
    "lo",
    "lu",
    "ly",
    "ma",
    "me",
    "mo",
    "mp",
    "my",
    "nd",
    "no",
    "nt",
    "ob",
    "oc",
    "od",
    "of",
    "ok",
    "ol",
    "om",
    "on",
    "oo",
    "op",
    "or",
    "os",
    "ot",
    "ou",
    "ov",
    "ow",
    "ph",
    "pl",
    "po",
    "pr",
    "pu",
    "qu",
    "ra",
    "re",
    "ro",
    "ru",
    "s-",
    "sc",
    "se",
    "sh",
    "si",
    "sk",
    "sl",
    "sn",
    "sp",
    "ss",
    "st",
    "su",
    "sw",
    "sy",
    "t-",
    "ta",
    "te",
    "th",
    "ti",
    "tn",
    "to",
    "tr",
    "ts",
    "tu",
    "ty",
    "ub",
    "ul",
    "um",
    "un",
    "up",
    "ur",
    "us",
    "ve",
    "vi",
    "we",
    "wh",
    "-",
    ".",
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z"
];
//# sourceMappingURL=network-hostname.js.map