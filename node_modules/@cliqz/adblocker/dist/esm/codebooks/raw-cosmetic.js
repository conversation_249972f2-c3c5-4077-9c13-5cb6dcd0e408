/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
/* eslint-disable prettier/prettier */
export default [
    "-webkit-touch-callo",
    ", 1year, , domain, ",
    ", googlesyndication",
    ", SOCS, CAISNQgQEit",
    ":style(overflow: au",
    "##^script:has-text(",
    "9udGVuZHVpc2VydmVyX",
    "GgJmaSADGgYIgOu0sgY",
    "ib3FfaWRlbnRpdHlmcm",
    "position: initial !",
    "set-local-storage-i",
    "set, blurred, false",
    "user-select: text !",
    "zIwMjQwNTE0LjA2X3Aw",
    "[href^=\"https://",
    "rmnt, script, ",
    "ut: default !",
    " !important)",
    "trusted-set-",
    ", document.",
    ", noopFunc)",
    "##body,html",
    "contextmenu",
    "no-fetch-if",
    "otification",
    ".com##+js(",
    "=\"https://",
    "background",
    "important;",
    " -webkit-",
    ".*,xhamst",
    "container",
    "AAAAAAAA",
    "nostif, ",
    ",google",
    ":style(",
    "consent",
    "message",
    "nowoif)",
    "privacy",
    "-wrapp",
    ",kayak",
    ".co.uk",
    "[class",
    "##+js(",
    "accept",
    "aopr, ",
    "banner",
    "bottom",
    "cookie",
    "Cookie",
    "google",
    "notice",
    "policy",
    "widget",
    ":has(",
    "##div",
    "block",
    "cript",
    "true)",
    ".co.",
    ".com",
    ".de,",
    ".fr,",
    ".net",
    ".nl,",
    ".pl,",
    ".xyz",
    "#@#.",
    "2%3A",
    "gdpr",
    "html",
    "ight",
    "news",
    "text",
    "to !",
    "wrap",
    "www.",
    " > ",
    ",xh",
    "##.",
    "###",
    "%3D",
    "%7C",
    "ent",
    "lay",
    "web",
    "__",
    "-s",
    ", ",
    ",b",
    ",c",
    ",f",
    ",g",
    ",h",
    ",m",
    ",p",
    ",s",
    ",t",
    ": ",
    ".*",
    ".b",
    ".c",
    ".m",
    ".p",
    ".s",
    "\"]",
    "##",
    "%2",
    "%5",
    "=\"",
    "00",
    "a-",
    "ab",
    "ac",
    "ad",
    "Ad",
    "af",
    "ag",
    "ak",
    "al",
    "am",
    "an",
    "ap",
    "ar",
    "as",
    "at",
    "au",
    "av",
    "ay",
    "az",
    "bo",
    "ch",
    "ck",
    "cl",
    "ct",
    "de",
    "di",
    "do",
    "e-",
    "ed",
    "el",
    "em",
    "en",
    "er",
    "es",
    "et",
    "ex",
    "fi",
    "fo",
    "he",
    "ic",
    "id",
    "if",
    "ig",
    "il",
    "im",
    "in",
    "is",
    "it",
    "iv",
    "le",
    "lo",
    "mo",
    "ol",
    "om",
    "on",
    "op",
    "or",
    "ot",
    "ov",
    "pl",
    "po",
    "re",
    "ro",
    "s_",
    "s-",
    "se",
    "sh",
    "si",
    "sp",
    "st",
    "t-",
    "th",
    "ti",
    "tr",
    "tv",
    "ub",
    "ul",
    "um",
    "un",
    "up",
    "ur",
    "us",
    "ut",
    "vi",
    " ",
    "_",
    "-",
    ",",
    ":",
    ".",
    "(",
    ")",
    "[",
    "*",
    "/",
    "^",
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "a",
    "b",
    "B",
    "c",
    "C",
    "d",
    "D",
    "e",
    "E",
    "f",
    "F",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "L",
    "m",
    "M",
    "n",
    "o",
    "p",
    "P",
    "q",
    "r",
    "s",
    "S",
    "t",
    "T",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z"
];
//# sourceMappingURL=raw-cosmetic.js.map