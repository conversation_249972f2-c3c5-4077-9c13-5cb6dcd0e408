/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
/* eslint-disable prettier/prettier */
export default [
    ",redirect=google-ima",
    "/js/sdkloader/ima3.j",
    "/wp-content/plugins/",
    ",redirect-rule=noop",
    ".com^$third-party",
    "googlesyndication",
    "imasdk.googleapis",
    ".cloudfront.net^",
    ",redirect-rule=",
    "$script,domain=",
    ",3p,denyallow=",
    ",redirect=noop",
    "xmlhttprequest",
    ".actonservice",
    "^$third-party",
    "||smetrics.",
    "third-party",
    "marketing.",
    "$document",
    "analytics",
    ",domain=",
    "/assets/",
    "metrics.",
    "subdocum",
    "tracking",
    "$script",
    ".co.uk",
    "$ghide",
    "a8clk.",
    "cookie",
    "google",
    "script",
    ".com^",
    ".xyz^",
    "$doma",
    "a8cv.",
    "image",
    "media",
    "track",
    ".com",
    ".fr^",
    ".gif",
    ".jp^",
    ".net",
    "/js/",
    "$doc",
    "$xhr",
    "www.",
    ",1p",
    ",3p",
    ".io",
    ".jp",
    ".js",
    "app",
    "cdn",
    "ent",
    "new",
    "web",
    ".b",
    ".c",
    ".d",
    ".f",
    ".h",
    ".m",
    ".n",
    ".p",
    ".s",
    ".t",
    "@@",
    "/*",
    "/p",
    "||",
    "a1",
    "ab",
    "ac",
    "ad",
    "af",
    "ag",
    "ai",
    "ak",
    "al",
    "am",
    "an",
    "ap",
    "ar",
    "as",
    "at",
    "au",
    "av",
    "aw",
    "ax",
    "ay",
    "az",
    "be",
    "bo",
    "br",
    "ca",
    "ce",
    "ch",
    "ck",
    "ct",
    "cu",
    "de",
    "di",
    "do",
    "e-",
    "e^",
    "ec",
    "ed",
    "el",
    "em",
    "en",
    "ep",
    "er",
    "es",
    "et",
    "ev",
    "ew",
    "ex",
    "fe",
    "ff",
    "fi",
    "fo",
    "fr",
    "g^",
    "ge",
    "gi",
    "go",
    "gr",
    "he",
    "hi",
    "ho",
    "hp",
    "ht",
    "ic",
    "id",
    "ig",
    "il",
    "im",
    "in",
    "io",
    "ip",
    "ir",
    "is",
    "it",
    "ix",
    "iz",
    "js",
    "ke",
    "le",
    "li",
    "lo",
    "lu",
    "ly",
    "me",
    "mo",
    "mp",
    "my",
    "ne",
    "no",
    "od",
    "ok",
    "ol",
    "om",
    "on",
    "op",
    "or",
    "ot",
    "pl",
    "po",
    "pr",
    "qu",
    "re",
    "ri",
    "ro",
    "ru",
    "s-",
    "s/",
    "sc",
    "se",
    "sh",
    "si",
    "so",
    "sp",
    "ss",
    "st",
    "su",
    "te",
    "th",
    "ti",
    "to",
    "tr",
    "ts",
    "ty",
    "ub",
    "ud",
    "ul",
    "um",
    "un",
    "up",
    "ur",
    "us",
    "ut",
    "ve",
    "vi",
    "we",
    "_",
    "-",
    ",",
    "?",
    ".",
    "*",
    "/",
    "^",
    "=",
    "|",
    "~",
    "$",
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z"
];
//# sourceMappingURL=raw-network.js.map