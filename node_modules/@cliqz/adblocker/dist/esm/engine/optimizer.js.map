{"version": 3, "file": "optimizer.js", "sourceRoot": "", "sources": ["../../../src/engine/optimizer.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,aAAa,EAAE,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC3E,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,SAAS,YAAY,CAAC,CAAS;IAC7B,OAAO,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;AAC3B,CAAC;AAED,SAAS,MAAM,CAAC,CAAS;IACvB,OAAO,MAAM,CAAC,CAAC,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC,GAAG,CAAC;AAC7D,CAAC;AAED,SAAS,cAAc,CAAO,GAAgB,EAAE,GAAM,EAAE,KAAQ;IAC9D,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,MAAM,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACvB,CAAC;IACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,CAAC;AAED,SAAS,OAAO,CACd,OAAwB,EACxB,QAA2C;IAE3C,MAAM,OAAO,GAAiC,IAAI,GAAG,EAAE,CAAC;IACxD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,OAAO,CACd,OAAwB,EACxB,SAA6C;IAK7C,MAAM,QAAQ,GAAoB,EAAE,CAAC;IACrC,MAAM,QAAQ,GAAoB,EAAE,CAAC;IAErC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YACtB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAED,OAAO;QACL,QAAQ;QACR,QAAQ;KACT,CAAC;AACJ,CAAC;AASD,MAAM,aAAa,GAAoB;IACrC;QACE,WAAW,EAAE,iCAAiC;QAC9C,MAAM,EAAE,CAAC,OAAwB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAChD,eAAe,EAAE,CAAC,MAAqB,EAAE,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE;QAC/D,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI;KACnB;IACD;QACE,WAAW,EAAE,8EAA8E;QAC3F,MAAM,EAAE,CAAC,OAAwB,EAAE,EAAE;YACnC,MAAM,KAAK,GAAa,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAgB,IAAI,GAAG,EAAE,CAAC;YACzC,MAAM,YAAY,GAAgB,IAAI,GAAG,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAgB,IAAI,GAAG,EAAE,CAAC;YACxC,MAAM,WAAW,GAAgB,IAAI,GAAG,EAAE,CAAC;YAE3C,KAAK,MAAM,EAAE,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC;gBAClC,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBAC1B,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;wBAChC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC5B,CAAC;oBAED,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;wBACpC,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;4BACrC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBACtB,CAAC;oBACH,CAAC;oBAED,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;wBACnC,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;4BACpC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBACrB,CAAC;oBACH,CAAC;oBAED,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;wBACvC,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;4BACxC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBACzB,CAAC;oBACH,CAAC;oBAED,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;wBACtC,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;4BACvC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBACxB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,IAAI,aAAa,CACtB,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;gBAC5B,OAAO,EAAE,IAAI,OAAO,CAAC;oBACnB,SAAS,EAAE,SAAS,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;oBAC/E,QAAQ,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;oBAC5E,YAAY,EACV,YAAY,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;oBAC5E,WAAW,EAAE,WAAW,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;oBACrF,KAAK,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;iBACxD,CAAC;gBACF,OAAO,EACL,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,SAAS;oBAC9B,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;oBACrD,CAAC,CAAC,SAAS;aAChB,CAAC,CACH,CAAC;QACJ,CAAC;QACD,eAAe,EAAE,CAAC,MAAqB,EAAE,EAAE,WACzC,OAAA,MAAM,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,MAAA,MAAM,CAAC,WAAW,mCAAI,EAAE,CAAC,CAAA,EAAA;QAC3F,MAAM,EAAE,CAAC,MAAqB,EAAE,EAAE,CAChC,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS;KACpF;IACD;QACE,WAAW,EAAE,6CAA6C;QAC1D,MAAM,EAAE,CAAC,OAAwB,EAAE,EAAE;YACnC,MAAM,QAAQ,GAAa,EAAE,CAAC;YAC9B,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;oBAChB,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBAC5C,CAAC;qBAAM,IAAI,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC;oBAC7B,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC7C,CAAC;qBAAM,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC;oBAC5B,QAAQ,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YAED,OAAO,IAAI,aAAa,CACtB,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;gBAC5B,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,mBAAmB,CAAC,OAAO,CAAC;gBAC1D,OAAO,EACL,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,SAAS;oBAC9B,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;oBACrD,CAAC,CAAC,SAAS;gBACf,KAAK,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACtC,CAAC,CACH,CAAC;QACJ,CAAC;QACD,eAAe,EAAE,CAAC,MAAqB,EAAE,EAAE,CACzC,EAAE,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,mBAAmB,CAAC,OAAO,GAAG,CAAC,mBAAmB,CAAC,WAAW,CAAC;QAC3F,MAAM,EAAE,CAAC,MAAqB,EAAE,EAAE,CAChC,MAAM,CAAC,OAAO,KAAK,SAAS;YAC5B,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAC1B,CAAC,MAAM,CAAC,UAAU,EAAE;YACpB,CAAC,MAAM,CAAC,KAAK,EAAE;KAClB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,OAAwB;IAC1D,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,OAAyB;IAC5D,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,OAAwB;IACtD,MAAM,KAAK,GAAoB,EAAE,CAAC;IAClC,IAAI,MAAM,GAAG,OAAO,CAAC;IAErB,KAAK,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,aAAa,EAAE,CAAC;QAChE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvD,MAAM,GAAG,QAAQ,CAAC;QAElB,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAClD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,MAAM,MAAM,IAAI,MAAM,EAAE,CAAC;QAC5B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}