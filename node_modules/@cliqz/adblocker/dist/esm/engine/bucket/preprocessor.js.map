{"version": 3, "file": "preprocessor.js", "sourceRoot": "", "sources": ["../../../../src/engine/bucket/preprocessor.ts"], "names": [], "mappings": "AAEA,OAAO,YAAqB,MAAM,uBAAuB,CAAC;AAI1D,MAAM,CAAC,OAAO,OAAO,kBAAkB;IAC9B,MAAM,CAAC,WAAW,CAAC,IAAoB;QAC5C,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAY,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACjC,CAAC;QAED,MAAM,aAAa,GAAmB,EAAE,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,IAAI,CAAC;YACd,QAAQ;YACR,aAAa;SACd,CAAC,CAAC;IACL,CAAC;IAKD,YAAY,EACV,QAAQ,GAAG,IAAI,GAAG,EAAE,EACpB,aAAa,GAAG,EAAE,GAInB;QACC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAEM,gBAAgB,CAAC,MAAe;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;IAC3C,CAAC;IAEM,SAAS,CAAC,GAAQ;QACvB,+CAA+C;QAC/C,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEtB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,KAAK,MAAM,QAAQ,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;oBAC9C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEM,MAAM,CACX,EACE,KAAK,EACL,OAAO,GAIR,EACD,GAAQ;QAER,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,MAAM,YAAY,IAAI,OAAO,EAAE,CAAC;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CACnC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,YAAY,CAAC,SAAS,CACtD,CAAC;gBAEF,kDAAkD;gBAClD,uEAAuE;gBACvE,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,SAAS;gBACX,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;oBAC9C,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,MAAM,YAAY,IAAI,KAAK,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CACnC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,YAAY,CAAC,SAAS,CACtD,CAAC;gBAEF,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAEtC,SAAS;gBACX,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;oBAC9C,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAEM,SAAS,CAAC,IAAoB;QACnC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC3C,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEM,iBAAiB;QACtB,IAAI,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEjD,aAAa,IAAI,CAAC,CAAC;QACnB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,aAAa,IAAI,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACpD,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;CACF"}