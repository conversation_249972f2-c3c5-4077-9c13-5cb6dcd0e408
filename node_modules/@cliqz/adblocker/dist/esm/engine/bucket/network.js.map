{"version": 3, "file": "network.js", "sourceRoot": "", "sources": ["../../../../src/engine/bucket/network.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAIH,OAAO,aAAa,MAAM,0BAA0B,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AACvE,OAAO,YAAY,MAAM,qBAAqB,CAAC;AAC/C,OAAO,gBAAgB,MAAM,cAAc,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,OAAO,OAAO,mBAAmB;IAC/B,MAAM,CAAC,WAAW,CAAC,MAAsB,EAAE,MAAc;QAC9D,MAAM,MAAM,GAAG,IAAI,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAEnD,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CACrC,MAAM,EACN,aAAa,CAAC,WAAW,EACzB,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,mBAAmB,EAClE,MAAM,CACP,CAAC;QAEF,MAAM,CAAC,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC,MAAM,EAAE,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAE5F,OAAO,MAAM,CAAC;IAChB,CAAC;IAcD,YAAY,EAAE,OAAO,GAAG,EAAE,EAAE,MAAM,EAAiD;QACjF,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,CAAC;YAC5B,MAAM;YACN,WAAW,EAAE,aAAa,CAAC,WAAW;YACtC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,mBAAmB;SAC7E,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,gBAAgB,CAAC;YACrC,MAAM;YACN,WAAW,EAAE,aAAa,CAAC,WAAW;YACtC,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAEM,UAAU;QACf,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;IAC/E,CAAC;IAEM,MAAM,CAAC,UAA2B,EAAE,cAAuC;QAChF,MAAM,UAAU,GAAoB,EAAE,CAAC;QACvC,MAAM,SAAS,GAAoB,EAAE,CAAC;QACtC,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;YAChC,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;gBACzB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC5B,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC9E,CAAC;IAEM,SAAS,CAAC,MAAsB;QACrC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAEM,QAAQ,CACb,OAAgB,EAChB,gBAAqD;QAErD,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,MAAqB,EAAE,EAAE;YAC5E,IACE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;gBACrB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,KAAK;gBACvC,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAG,MAAM,CAAC,CAAA,EAC3B,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CACV,OAAgB,EAChB,gBAAqD;QAErD,IAAI,KAAgC,CAAC;QAErC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,MAAqB,EAAE,EAAE;YAC5E,IACE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;gBACrB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,KAAK;gBACvC,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAG,MAAM,CAAC,CAAA,EAC3B,CAAC;gBACD,KAAK,GAAG,MAAM,CAAC;gBACf,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAqB;QAC5C,yEAAyE;QACzE,kEAAkE;QAClE,0EAA0E;QAC1E,4DAA4D;QAC5D,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAEhD,mDAAmD;YACnD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,+CAA+C;YAC/C,MAAM,aAAa,GAAgB,IAAI,GAAG,EAAE,CAAC;YAC7C,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACrC,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;IAChD,CAAC;CACF"}