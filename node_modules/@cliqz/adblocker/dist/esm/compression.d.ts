/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
import { Smaz } from '@remusao/smaz';
export default class Compression {
    readonly cosmeticSelector: Smaz;
    readonly networkCSP: Smaz;
    readonly networkRedirect: Smaz;
    readonly networkHostname: Smaz;
    readonly networkFilter: Smaz;
    readonly networkRaw: Smaz;
    readonly cosmeticRaw: Smaz;
}
//# sourceMappingURL=compression.d.ts.map