/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
import { Domains } from '../engine/domains.js';
import { StaticDataView } from '../data-view.js';
import Request, { RequestType } from '../request.js';
import IFilter from './interface.js';
import { HTMLModifier } from '../html-filtering.js';
import type CosmeticFilter from './cosmetic.js';
export declare function normalizeRawFilterOptions(rawFilter: string): string;
/**
 * Masks used to store options of network filters in a bitmask.
 */
export declare const enum NETWORK_FILTER_MASK {
    fromDocument = 1,
    fromFont = 2,
    fromHttp = 4,
    fromHttps = 8,
    fromImage = 16,
    fromMedia = 32,
    fromObject = 64,
    fromOther = 128,
    fromPing = 256,
    fromScript = 512,
    fromStylesheet = 1024,
    fromSubdocument = 2048,
    fromWebsocket = 4096,// e.g.: ws, wss
    fromXmlHttpRequest = 8192,
    firstParty = 16384,
    thirdParty = 32768,
    isReplace = 65536,
    isBadFilter = 131072,
    isCSP = 262144,
    isGenericHide = 524288,
    isImportant = 1048576,
    isSpecificHide = 2097152,
    isFullRegex = 4194304,
    isRegex = 8388608,
    isUnicode = 16777216,
    isLeftAnchor = 33554432,
    isRightAnchor = 67108864,
    isException = 134217728,
    isHostnameAnchor = 268435456,
    isRedirectRule = 536870912,
    isRedirect = 1073741824
}
/**
 * Transforms the replace modifier option value into the regular expression with its replacement.
 * This function takes a fixed length array from `getFilterReplaceOptionValue`
 * then try to build the regular expression.
 * This function will return `null` if the array format or the given regular expression components are not valid.
 */
export declare function replaceOptionValueToRegexp(value: string): HTMLModifier | null;
export default class NetworkFilter implements IFilter {
    static parse(line: string, debug?: boolean): NetworkFilter | null;
    /**
     * Deserialize network filters. The code accessing the buffer should be
     * symetrical to the one in `serializeNetworkFilter`.
     */
    static deserialize(buffer: StaticDataView): NetworkFilter;
    readonly filter: string | undefined;
    readonly hostname: string | undefined;
    readonly mask: number;
    readonly domains: Domains | undefined;
    readonly denyallow: Domains | undefined;
    readonly optionValue: string | undefined;
    readonly rawLine: string | undefined;
    id: number | undefined;
    regex: RegExp | undefined;
    constructor({ filter, hostname, mask, domains, denyallow, optionValue, rawLine, regex, }: {
        filter: string | undefined;
        hostname: string | undefined;
        mask: number;
        domains: Domains | undefined;
        denyallow: Domains | undefined;
        optionValue: string | undefined;
        rawLine: string | undefined;
        regex: RegExp | undefined;
    });
    get csp(): string | undefined;
    get redirect(): string | undefined;
    isCosmeticFilter(): this is CosmeticFilter;
    isNetworkFilter(): this is NetworkFilter;
    match(request: Request): boolean;
    /**
     * To allow for a more compact representation of network filters, the
     * representation is composed of a mandatory header, and some optional
     *
     * Header:
     * =======
     *
     *  | opt | mask
     *     8     32
     *
     * For an empty filter having no pattern, hostname, the minimum size is: 42 bits.
     *
     * Then for each optional part (filter, hostname optDomains, optNotDomains,
     * redirect), it takes 16 bits for the length of the string + the length of the
     * string in bytes.
     *
     * The optional parts are written in order of there number of occurrence in the
     * filter list used by the adblocker. The most common being `hostname`, then
     * `filter`, `optDomains`, `optNotDomains`, `redirect`.
     *
     * Example:
     * ========
     *
     * @@||cliqz.com would result in a serialized version:
     *
     * | 1 | mask | 9 | c | l | i | q | z | . | c | o | m  (16 bytes)
     *
     * In this case, the serialized version is actually bigger than the original
     * filter, but faster to deserialize. In the future, we could optimize the
     * representation to compact small filters better.
     *
     * Ideas:
     *  * variable length encoding for the mask (if not option, take max 1 byte).
     *  * first byte could contain the mask as well if small enough.
     *  * when packing ascii string, store several of them in each byte.
     */
    serialize(buffer: StaticDataView): void;
    getSerializedSize(compression: boolean): number;
    /**
     * Tries to recreate the original representation of the filter (adblock
     * syntax) from the internal representation. If `rawLine` is set (when filters
     * are parsed in `debug` mode for example), then it is returned directly.
     * Otherwise, we try to stick as closely as possible to the original form;
     * there are things which cannot be recovered though, like domains options
     * of which only hashes are stored.
     */
    toString(modifierReplacer?: (modifier: string) => string): string;
    getIdWithoutBadFilter(): number;
    getId(): number;
    hasFilter(): boolean;
    hasDomains(): boolean;
    getMask(): number;
    getCptMask(): number;
    isRedirect(): boolean;
    isRedirectRule(): boolean;
    getRedirect(): string;
    isReplace(): boolean;
    getHtmlModifier(): HTMLModifier | null;
    isHtmlFilteringRule(): boolean;
    getRedirectResource(): string;
    getRedirectPriority(): number;
    hasHostname(): boolean;
    getHostname(): string;
    getFilter(): string;
    getRegex(): RegExp;
    getTokens(): Uint32Array[];
    /**
     * Check if this filter should apply to a request with this content type.
     */
    isCptAllowed(cpt: RequestType): boolean;
    isException(): boolean;
    isHostnameAnchor(): boolean;
    isRightAnchor(): boolean;
    isLeftAnchor(): boolean;
    isImportant(): boolean;
    isFullRegex(): boolean;
    isRegex(): boolean;
    isPlain(): boolean;
    isCSP(): boolean;
    isElemHide(): boolean;
    isSpecificHide(): boolean;
    isGenericHide(): boolean;
    isBadFilter(): boolean;
    isUnicode(): boolean;
    fromAny(): boolean;
    thirdParty(): boolean;
    firstParty(): boolean;
    fromImage(): boolean;
    fromMedia(): boolean;
    fromObject(): boolean;
    fromOther(): boolean;
    fromPing(): boolean;
    fromScript(): boolean;
    fromStylesheet(): boolean;
    fromDocument(): boolean;
    fromSubdocument(): boolean;
    fromWebsocket(): boolean;
    fromHttp(): boolean;
    fromHttps(): boolean;
    fromXmlHttpRequest(): boolean;
    fromFont(): boolean;
}
/**
 * Handle hostname anchored filters, given 'hostname' from ||hostname and
 * request's hostname, check if there is a match. This is tricky because
 * filters authors rely and different assumptions. We can have prefix of suffix
 * matches of anchor.
 */
export declare function isAnchoredByHostname(filterHostname: string, hostname: string, isFollowedByWildcard: boolean): boolean;
//# sourceMappingURL=network.d.ts.map