{"version": 3, "file": "request.d.ts", "sourceRoot": "", "sources": ["../../src/request.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAgBH,MAAM,MAAM,mBAAmB,GAC3B,WAAW,GACX,UAAU,GACV,YAAY,GACZ,QAAQ,GACR,OAAO,GACP,MAAM,GACN,QAAQ,GACR,KAAK,GACL,MAAM,GACN,WAAW,GACX,OAAO,GACP,WAAW,GACX,OAAO,CAAC;AAGZ,MAAM,MAAM,oBAAoB,GAC5B,oBAAoB,GACpB,UAAU,GACV,aAAa,GACb,OAAO,GACP,MAAM,GACN,OAAO,GACP,UAAU,GACV,OAAO,GACP,OAAO,GACP,UAAU,GACV,WAAW,GACX,QAAQ,GACR,gBAAgB,GAChB,YAAY,GACZ,WAAW,GACX,WAAW,GACX,KAAK,CAAC;AAEV,MAAM,MAAM,qBAAqB,GAC7B,UAAU,GACV,aAAa,GACb,OAAO,GACP,MAAM,GACN,OAAO,GACP,UAAU,GACV,OAAO,GACP,OAAO,GACP,QAAQ,GACR,YAAY,GACZ,WAAW,GACX,WAAW,GACX,KAAK,CAAC;AAGV,MAAM,MAAM,oBAAoB,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC;AAGlE,MAAM,MAAM,qBAAqB,GAAG,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC;AAGpE,MAAM,MAAM,cAAc,GAAG,oBAAoB,GAAG,qBAAqB,CAAC;AAG1E,MAAM,MAAM,WAAW,GACnB,cAAc,GACd,mBAAmB,GACnB,oBAAoB,GACpB,qBAAqB,CAAC;AAE1B,eAAO,MAAM,qBAAqB,EAAE;KAAG,CAAC,IAAI,WAAW,GAAG,MAAM;CAmC/D,CAAC;AAEF,wBAAgB,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAM7D;AAED,wBAAgB,2BAA2B,CACzC,QAAQ,EAAE,MAAM,EAChB,GAAG,EAAE,MAAM,EACX,aAAa,EAAE,MAAM,GACpB,WAAW,CAmBb;AAED;;;;;GAKG;AACH,wBAAgB,8BAA8B,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,CAU9F;AAED,wBAAgB,iCAAiC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,WAAW,CAU/F;AAED,wBAAgB,mCAAmC,CACjD,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,GACb,WAAW,CAEb;AAsBD,MAAM,WAAW,qBAAqB,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS;IACxD,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IAEd,GAAG,EAAE,MAAM,CAAC;IACZ,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IAEf,SAAS,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;IAErB,IAAI,EAAE,WAAW,CAAC;IAQlB,uBAAuB,EAAE,CAAC,CAAC;CAC5B;AAED,MAAM,CAAC,OAAO,OAAO,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS;IAC9C;;OAEG;WACW,cAAc,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,EAAE,EAChD,SAAe,EACf,KAAS,EACT,GAAQ,EACR,QAAQ,EACR,MAAM,EACN,SAAc,EACd,cAAc,EACd,YAAY,EACZ,IAAmB,EACnB,uBAAuB,GACxB,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAkCjD,SAAgB,uBAAuB,EAAE,CAAC,CAAC;IAEpC,IAAI,EAAE,WAAW,CAAC;IACzB,SAAgB,MAAM,EAAE,OAAO,CAAC;IAChC,SAAgB,OAAO,EAAE,OAAO,CAAC;IACjC,SAAgB,WAAW,EAAE,OAAO,CAAC;IACrC,SAAgB,YAAY,EAAE,OAAO,CAAC;IACtC,SAAgB,YAAY,EAAE,OAAO,CAAC;IAEtC,SAAgB,EAAE,EAAE,MAAM,CAAC;IAC3B,SAAgB,KAAK,EAAE,MAAM,CAAC;IAC9B,SAAgB,GAAG,EAAE,MAAM,CAAC;IAC5B,SAAgB,QAAQ,EAAE,MAAM,CAAC;IACjC,SAAgB,MAAM,EAAE,MAAM,CAAC;IAE/B,SAAgB,oBAAoB,EAAE,WAAW,CAAC;IAClD,SAAgB,kBAAkB,EAAE,WAAW,CAAC;IAGhD,OAAO,CAAC,MAAM,CAAsC;IACpD,OAAO,CAAC,cAAc,CAAsC;IAC5D,OAAO,CAAC,YAAY,CAAsC;gBAE9C,EACV,SAAS,EACT,KAAK,EAEL,IAAI,EAEJ,MAAM,EACN,QAAQ,EACR,GAAG,EAEH,YAAY,EACZ,cAAc,EAEd,uBAAuB,GACxB,EAAE,qBAAqB;IAqDjB,iBAAiB,IAAI,WAAW;IAWhC,eAAe,IAAI,WAAW;IAW9B,SAAS,IAAI,WAAW;IAmBxB,WAAW,IAAI,OAAO;IAItB,UAAU,IAAI,OAAO;IAI5B;;;;;OAKG;IACI,kBAAkB,IAAI,WAAW;CAQzC;AAED;;;GAGG;AACH,wBAAgB,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,qBAAqB,CAAC,GAAG,OAAO,CAE5E"}