{"name": "@cliqz/adblocker", "version": "1.34.0", "description": "Ghostery adblocker library", "author": {"name": "Ghostery"}, "homepage": "https://github.com/ghostery/adblocker#readme", "license": "MPL-2.0", "type": "module", "tshy": {"project": "./tsconfig.json", "exports": {"./package.json": "./package.json", ".": "./src/index.js"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "files": ["LICENSE", "dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+ssh://**************/ghostery/adblocker.git", "directory": "packages/adblocker"}, "scripts": {"clean": "rimraf dist coverage .tshy .tshy-build .nyc_output .rollup.cache", "lint": "eslint src test tools", "build": "tshy && rollup --config ./rollup.config.js", "test": "nyc mocha", "dev": "mocha --watch", "bench-metadata": "tsx ./tools/bench-metadata.ts", "bump-internal-engine-version": "tsx ./tools/auto-bump-engine-version.ts", "codebook-network-csp": "tsx ./tools/generate_compression_codebooks.ts -- network-csp", "codebook-network-redirect": "tsx ./tools/generate_compression_codebooks.ts -- network-redirect", "codebook-network-filter": "tsx ./tools/generate_compression_codebooks.ts -- network-filter", "codebook-network-hostname": "tsx ./tools/generate_compression_codebooks.ts -- network-hostname", "codebook-cosmetic-selector": "tsx ./tools/generate_compression_codebooks.ts -- cosmetic-selector", "codebook-raw-network": "tsx ./tools/generate_compression_codebooks.ts -- raw-network", "codebook-raw-cosmetic": "tsx ./tools/generate_compression_codebooks.ts -- raw-cosmetic", "generate-codebooks": "concurrently -n build: \"yarn:codebook-*\" && yarn bump-internal-engine-version"}, "bugs": {"url": "https://github.com/ghostery/adblocker/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "zhong<PERSON>@cliqz.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Eleni", "email": "<EMAIL>"}, {"name": "ecnmst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "devDependencies": {"@remusao/smaz-generate": "^1.9.1", "@rollup/plugin-terser": "^0.4.4", "@types/chai": "^5.0.0", "@types/mocha": "^10.0.1", "@types/node": "^22.0.2", "axios": "^1.7.2", "chai": "^5.1.0", "concurrently": "^9.0.0", "eslint": "^9.3.0", "mocha": "^10.2.0", "nyc": "^17.0.0", "rimraf": "^6.0.1", "rollup": "^4.0.2", "tshy": "^3.0.2", "tsx": "^4.16.2", "typescript": "^5.5.2"}, "dependencies": {"@cliqz/adblocker-content": "^1.34.0", "@cliqz/adblocker-extended-selectors": "^1.34.0", "@remusao/guess-url-type": "^1.3.0", "@remusao/small": "^1.2.1", "@remusao/smaz": "^1.9.1", "@types/chrome": "^0.0.278", "@types/firefox-webext-browser": "^120.0.0", "tldts-experimental": "^6.0.14"}, "gitHead": "a68f58dfe72eb92ebf8ab1836608aa752f0d13e9"}