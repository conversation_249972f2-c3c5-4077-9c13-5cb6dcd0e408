{"version": 3, "file": "adblocker.umd.min.js", "sources": ["esm/index.js"], "sourcesContent": ["/*!\n * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.\n *\n * This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/.\n */\nconst SCRIPT_ID = 'cliqz-adblocker-script';\nconst IGNORED_TAGS = new Set(['br', 'head', 'link', 'meta', 'script', 'style', 's']);\nfunction debounce(fn, { waitFor, maxWait, }) {\n    let delayedTimer;\n    let maxWaitTimer;\n    const clear = () => {\n        clearTimeout(delayedTimer);\n        clearTimeout(maxWaitTimer);\n        delayedTimer = undefined;\n        maxWaitTimer = undefined;\n    };\n    const run = () => {\n        clear();\n        fn();\n    };\n    return [\n        () => {\n            if (maxWait > 0 && maxWaitTimer === undefined) {\n                maxWaitTimer = setTimeout(run, maxWait);\n            }\n            clearTimeout(delayedTimer);\n            delayedTimer = setTimeout(run, waitFor);\n        },\n        clear,\n    ];\n}\nfunction isElement(node) {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType#node_type_constants\n    return node.nodeType === 1; // Node.ELEMENT_NODE;\n}\nfunction getElementsFromMutations(mutations) {\n    // Accumulate all nodes which were updated in `nodes`\n    const elements = [];\n    for (const mutation of mutations) {\n        if (mutation.type === 'attributes') {\n            if (isElement(mutation.target)) {\n                elements.push(mutation.target);\n            }\n        }\n        else if (mutation.type === 'childList') {\n            for (const addedNode of mutation.addedNodes) {\n                if (isElement(addedNode) && addedNode.id !== SCRIPT_ID) {\n                    elements.push(addedNode);\n                }\n            }\n        }\n    }\n    return elements;\n}\n/**\n * WARNING: this function should be self-contained and not rely on any global\n * symbol. That constraint needs to be fulfilled because this function can\n * potentially be injected in content-script (e.g.: see PuppeteerBlocker for\n * more details).\n */\nexport function extractFeaturesFromDOM(roots) {\n    // NOTE: This cannot be global as puppeteer needs to be able to serialize this function.\n    const ignoredTags = new Set(['br', 'head', 'link', 'meta', 'script', 'style', 's']);\n    const classes = new Set();\n    const hrefs = new Set();\n    const ids = new Set();\n    const seenElements = new Set();\n    for (const root of roots) {\n        for (const element of [\n            root,\n            ...root.querySelectorAll('[id]:not(html):not(body),[class]:not(html):not(body),[href]:not(html):not(body)'),\n        ]) {\n            // If one of root belongs to another root which is parent node of the one, querySelectorAll can return duplicates.\n            if (seenElements.has(element)) {\n                continue;\n            }\n            seenElements.add(element);\n            // Any conditions to filter this element out should be placed under this line:\n            if (ignoredTags.has(element.nodeName.toLowerCase())) {\n                continue;\n            }\n            // Update ids\n            const id = element.getAttribute('id');\n            if (typeof id === 'string') {\n                ids.add(id);\n            }\n            // Update classes\n            const classList = element.classList;\n            for (const classEntry of classList) {\n                classes.add(classEntry);\n            }\n            // Update href\n            const href = element.getAttribute('href');\n            if (typeof href === 'string') {\n                hrefs.add(href);\n            }\n        }\n    }\n    return {\n        classes: Array.from(classes),\n        hrefs: Array.from(hrefs),\n        ids: Array.from(ids),\n    };\n}\nexport class DOMMonitor {\n    constructor(cb) {\n        this.cb = cb;\n        this.knownIds = new Set();\n        this.knownHrefs = new Set();\n        this.knownClasses = new Set();\n        this.observer = null;\n    }\n    queryAll(window) {\n        this.cb({ type: 'elements', elements: [window.document.documentElement] });\n        this.handleUpdatedNodes([window.document.documentElement]);\n    }\n    start(window) {\n        if (this.observer === null && window.MutationObserver !== undefined) {\n            const nodes = new Set();\n            const handleUpdatedNodesCallback = () => {\n                this.handleUpdatedNodes(Array.from(nodes));\n                nodes.clear();\n            };\n            const [debouncedHandleUpdatedNodes, cancelHandleUpdatedNodes] = debounce(handleUpdatedNodesCallback, {\n                waitFor: 25,\n                maxWait: 1000,\n            });\n            this.observer = new window.MutationObserver((mutations) => {\n                getElementsFromMutations(mutations).forEach(nodes.add, nodes);\n                // Set a threshold to prevent websites continuously\n                // causing DOM mutations making the set being filled up infinitely.\n                if (nodes.size > 512) {\n                    cancelHandleUpdatedNodes();\n                    handleUpdatedNodesCallback();\n                }\n                else {\n                    debouncedHandleUpdatedNodes();\n                }\n            });\n            this.observer.observe(window.document.documentElement, {\n                // Monitor some attributes\n                attributes: true,\n                attributeFilter: ['class', 'id', 'href'],\n                childList: true,\n                subtree: true,\n            });\n        }\n    }\n    stop() {\n        if (this.observer !== null) {\n            this.observer.disconnect();\n            this.observer = null;\n        }\n    }\n    handleNewFeatures({ hrefs, ids, classes, }) {\n        const newIds = [];\n        const newClasses = [];\n        const newHrefs = [];\n        // Update ids\n        for (const id of ids) {\n            if (this.knownIds.has(id) === false) {\n                newIds.push(id);\n                this.knownIds.add(id);\n            }\n        }\n        for (const cls of classes) {\n            if (this.knownClasses.has(cls) === false) {\n                newClasses.push(cls);\n                this.knownClasses.add(cls);\n            }\n        }\n        for (const href of hrefs) {\n            if (this.knownHrefs.has(href) === false) {\n                newHrefs.push(href);\n                this.knownHrefs.add(href);\n            }\n        }\n        if (newIds.length !== 0 || newClasses.length !== 0 || newHrefs.length !== 0) {\n            this.cb({\n                type: 'features',\n                classes: newClasses,\n                hrefs: newHrefs,\n                ids: newIds,\n            });\n            return true;\n        }\n        return false;\n    }\n    handleUpdatedNodes(elements) {\n        if (elements.length !== 0) {\n            this.cb({\n                type: 'elements',\n                elements: elements.filter((e) => IGNORED_TAGS.has(e.nodeName.toLowerCase()) === false),\n            });\n            return this.handleNewFeatures(extractFeaturesFromDOM(elements));\n        }\n        return false;\n    }\n}\n/**\n * Wrap a self-executing script into a block of custom logic to remove the\n * script tag once execution is terminated. This can be useful to not leave\n * traces in the DOM after injections.\n */\nexport function autoRemoveScript(script) {\n    // Minified using 'terser'\n    return `try{${script}}catch(c){}!function(){var c=document.currentScript,e=c&&c.parentNode;e&&e.removeChild(c)}();`;\n    // Original:\n    //\n    //    try {\n    //      ${script}\n    //    } catch (ex) { }\n    //\n    //    (function() {\n    //      var currentScript = document.currentScript;\n    //      var parent = currentScript && currentScript.parentNode;\n    //\n    //      if (parent) {\n    //        parent.removeChild(currentScript);\n    //      }\n    //    })();\n}\nfunction insertNode(node, document) {\n    const parent = document.head || document.documentElement || document;\n    if (parent !== null) {\n        parent.appendChild(node);\n    }\n}\nfunction injectScriptlet(s, doc) {\n    const script = doc.createElement('script');\n    script.type = 'text/javascript';\n    script.id = SCRIPT_ID;\n    script.async = false;\n    script.appendChild(doc.createTextNode(autoRemoveScript(s)));\n    insertNode(script, doc);\n}\nfunction isFirefox(doc) {\n    var _a, _b, _c;\n    try {\n        return ((_c = (_b = (_a = doc.defaultView) === null || _a === void 0 ? void 0 : _a.navigator) === null || _b === void 0 ? void 0 : _b.userAgent) === null || _c === void 0 ? void 0 : _c.indexOf('Firefox')) !== -1;\n    }\n    catch (e) {\n        return false;\n    }\n}\nasync function injectScriptletFirefox(s, doc) {\n    const win = doc.defaultView;\n    const script = doc.createElement('script');\n    script.async = false;\n    script.id = SCRIPT_ID;\n    const blob = new win.Blob([autoRemoveScript(s)], { type: 'text/javascript; charset=utf-8' });\n    const url = win.URL.createObjectURL(blob);\n    // a hack for tests to that allows for async URL.createObjectURL\n    // eslint-disable-next-line @typescript-eslint/await-thenable\n    script.src = await url;\n    insertNode(script, doc);\n    win.URL.revokeObjectURL(url);\n}\nexport function injectScript(s, doc) {\n    if (isFirefox(doc)) {\n        injectScriptletFirefox(s, doc);\n    }\n    else {\n        injectScriptlet(s, doc);\n    }\n}\n//# sourceMappingURL=index.js.map"], "names": ["SCRIPT_ID", "IGNORED_TAGS", "Set", "isElement", "node", "nodeType", "extractFeaturesFromDOM", "roots", "ignoredTags", "classes", "hrefs", "ids", "seenElements", "root", "element", "querySelectorAll", "has", "add", "nodeName", "toLowerCase", "id", "getAttribute", "classList", "classEntry", "href", "Array", "from", "autoRemoveScript", "script", "insertNode", "document", "parent", "head", "documentElement", "append<PERSON><PERSON><PERSON>", "constructor", "cb", "this", "knownIds", "knownHrefs", "knownClasses", "observer", "queryAll", "window", "type", "elements", "handleUpdatedNodes", "start", "undefined", "MutationObserver", "nodes", "handleUpdatedNodesCallback", "clear", "debouncedHandleUpdatedNodes", "cancelHandleUpdatedNodes", "fn", "waitFor", "max<PERSON><PERSON>", "delayedTimer", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "run", "setTimeout", "debounce", "mutations", "mutation", "target", "push", "addedNode", "addedNodes", "getElementsFromMutations", "for<PERSON>ach", "size", "observe", "attributes", "attributeFilter", "childList", "subtree", "stop", "disconnect", "handleNewFeatures", "newIds", "newClasses", "newHrefs", "cls", "length", "filter", "e", "s", "doc", "_a", "_b", "_c", "defaultView", "navigator", "userAgent", "indexOf", "isFirefox", "createElement", "async", "createTextNode", "injectScriptlet", "win", "blob", "Blob", "url", "URL", "createObjectURL", "src", "revokeObjectURL", "injectScriptletFirefox"], "mappings": "iPAOA,MAAMA,EAAY,yBACZC,EAAe,IAAIC,IAAI,CAAC,KAAM,OAAQ,OAAQ,OAAQ,SAAU,QAAS,MAyB/E,SAASC,EAAUC,GAEf,OAAyB,IAAlBA,EAAKC,QAChB,CA0BO,SAASC,EAAuBC,GAEnC,MAAMC,EAAc,IAAIN,IAAI,CAAC,KAAM,OAAQ,OAAQ,OAAQ,SAAU,QAAS,MACxEO,EAAU,IAAIP,IACdQ,EAAQ,IAAIR,IACZS,EAAM,IAAIT,IACVU,EAAe,IAAIV,IACzB,IAAK,MAAMW,KAAQN,EACf,IAAK,MAAMO,IAAW,CAClBD,KACGA,EAAKE,iBAAiB,oFAC1B,CAEC,GAAIH,EAAaI,IAAIF,GACjB,SAIJ,GAFAF,EAAaK,IAAIH,GAEbN,EAAYQ,IAAIF,EAAQI,SAASC,eACjC,SAGJ,MAAMC,EAAKN,EAAQO,aAAa,MACd,iBAAPD,GACPT,EAAIM,IAAIG,GAGZ,MAAME,EAAYR,EAAQQ,UAC1B,IAAK,MAAMC,KAAcD,EACrBb,EAAQQ,IAAIM,GAGhB,MAAMC,EAAOV,EAAQO,aAAa,QACd,iBAATG,GACPd,EAAMO,IAAIO,EAEjB,CAEL,MAAO,CACHf,QAASgB,MAAMC,KAAKjB,GACpBC,MAAOe,MAAMC,KAAKhB,GAClBC,IAAKc,MAAMC,KAAKf,GAExB,CAqGO,SAASgB,EAAiBC,GAE7B,MAAO,OAAOA,gGAelB,CACA,SAASC,EAAWzB,EAAM0B,GACtB,MAAMC,EAASD,EAASE,MAAQF,EAASG,iBAAmBH,EAC7C,OAAXC,GACAA,EAAOG,YAAY9B,EAE3B,cA3HO,MACH,WAAA+B,CAAYC,GACRC,KAAKD,GAAKA,EACVC,KAAKC,SAAW,IAAIpC,IACpBmC,KAAKE,WAAa,IAAIrC,IACtBmC,KAAKG,aAAe,IAAItC,IACxBmC,KAAKI,SAAW,IACnB,CACD,QAAAC,CAASC,GACLN,KAAKD,GAAG,CAAEQ,KAAM,WAAYC,SAAU,CAACF,EAAOb,SAASG,mBACvDI,KAAKS,mBAAmB,CAACH,EAAOb,SAASG,iBAC5C,CACD,KAAAc,CAAMJ,GACF,GAAsB,OAAlBN,KAAKI,eAAiDO,IAA5BL,EAAOM,iBAAgC,CACjE,MAAMC,EAAQ,IAAIhD,IACZiD,EAA6B,KAC/Bd,KAAKS,mBAAmBrB,MAAMC,KAAKwB,IACnCA,EAAME,OAAO,GAEVC,EAA6BC,GApHhD,SAAkBC,GAAIC,QAAEA,EAAOC,QAAEA,IAC7B,IAAIC,EACAC,EACJ,MAAMP,EAAQ,KACVQ,aAAaF,GACbE,aAAaD,GACbD,OAAeV,EACfW,OAAeX,CAAS,EAEtBa,EAAM,KACRT,IACAG,GAAI,EAER,MAAO,CACH,KACQE,EAAU,QAAsBT,IAAjBW,IACfA,EAAeG,WAAWD,EAAKJ,IAEnCG,aAAaF,GACbA,EAAeI,WAAWD,EAAKL,EAAQ,EAE3CJ,EAER,CA6F4EW,CAASZ,EAA4B,CACjGK,QAAS,GACTC,QAAS,MAEbpB,KAAKI,SAAW,IAAIE,EAAOM,kBAAkBe,KA5FzD,SAAkCA,GAE9B,MAAMnB,EAAW,GACjB,IAAK,MAAMoB,KAAYD,EACnB,GAAsB,eAAlBC,EAASrB,KACLzC,EAAU8D,EAASC,SACnBrB,EAASsB,KAAKF,EAASC,aAG1B,GAAsB,cAAlBD,EAASrB,KACd,IAAK,MAAMwB,KAAaH,EAASI,WACzBlE,EAAUiE,IAAcA,EAAUhD,KAAOpB,GACzC6C,EAASsB,KAAKC,GAK9B,OAAOvB,CACX,EA2EgByB,CAAyBN,GAAWO,QAAQrB,EAAMjC,IAAKiC,GAGnDA,EAAMsB,KAAO,KACblB,IACAH,KAGAE,GACH,IAELhB,KAAKI,SAASgC,QAAQ9B,EAAOb,SAASG,gBAAiB,CAEnDyC,YAAY,EACZC,gBAAiB,CAAC,QAAS,KAAM,QACjCC,WAAW,EACXC,SAAS,GAEhB,CACJ,CACD,IAAAC,GAC0B,OAAlBzC,KAAKI,WACLJ,KAAKI,SAASsC,aACd1C,KAAKI,SAAW,KAEvB,CACD,iBAAAuC,EAAkBtE,MAAEA,EAAKC,IAAEA,EAAGF,QAAEA,IAC5B,MAAMwE,EAAS,GACTC,EAAa,GACbC,EAAW,GAEjB,IAAK,MAAM/D,KAAMT,GACiB,IAA1B0B,KAAKC,SAAStB,IAAII,KAClB6D,EAAOd,KAAK/C,GACZiB,KAAKC,SAASrB,IAAIG,IAG1B,IAAK,MAAMgE,KAAO3E,GACqB,IAA/B4B,KAAKG,aAAaxB,IAAIoE,KACtBF,EAAWf,KAAKiB,GAChB/C,KAAKG,aAAavB,IAAImE,IAG9B,IAAK,MAAM5D,KAAQd,GACmB,IAA9B2B,KAAKE,WAAWvB,IAAIQ,KACpB2D,EAAShB,KAAK3C,GACda,KAAKE,WAAWtB,IAAIO,IAG5B,OAAsB,IAAlByD,EAAOI,QAAsC,IAAtBH,EAAWG,QAAoC,IAApBF,EAASE,UAC3DhD,KAAKD,GAAG,CACJQ,KAAM,WACNnC,QAASyE,EACTxE,MAAOyE,EACPxE,IAAKsE,KAEF,EAGd,CACD,kBAAAnC,CAAmBD,GACf,OAAwB,IAApBA,EAASwC,SACThD,KAAKD,GAAG,CACJQ,KAAM,WACNC,SAAUA,EAASyC,QAAQC,IAAqD,IAA/CtF,EAAae,IAAIuE,EAAErE,SAASC,mBAE1DkB,KAAK2C,kBAAkB1E,EAAuBuC,IAG5D,kEA6DE,SAAsB2C,EAAGC,IAtBhC,SAAmBA,GACf,IAAIC,EAAIC,EAAIC,EACZ,IACI,OAAkN,KAA7D,QAA5IA,EAAyF,QAAnFD,EAAgC,QAA1BD,EAAKD,EAAII,mBAAgC,IAAPH,OAAgB,EAASA,EAAGI,iBAA8B,IAAPH,OAAgB,EAASA,EAAGI,iBAA8B,IAAPH,OAAgB,EAASA,EAAGI,QAAQ,WACpM,CACD,MAAOT,GACH,OAAO,CACV,CACL,CAeQU,CAAUR,GA/BlB,SAAyBD,EAAGC,GACxB,MAAM7D,EAAS6D,EAAIS,cAAc,UACjCtE,EAAOgB,KAAO,kBACdhB,EAAOR,GAAKpB,EACZ4B,EAAOuE,OAAQ,EACfvE,EAAOM,YAAYuD,EAAIW,eAAezE,EAAiB6D,KACvD3D,EAAWD,EAAQ6D,EACvB,CA4BQY,CAAgBb,EAAGC,GAlB3BU,eAAsCX,EAAGC,GACrC,MAAMa,EAAMb,EAAII,YACVjE,EAAS6D,EAAIS,cAAc,UACjCtE,EAAOuE,OAAQ,EACfvE,EAAOR,GAAKpB,EACZ,MAAMuG,EAAO,IAAID,EAAIE,KAAK,CAAC7E,EAAiB6D,IAAK,CAAE5C,KAAM,mCACnD6D,EAAMH,EAAII,IAAIC,gBAAgBJ,GAGpC3E,EAAOgF,UAAYH,EACnB5E,EAAWD,EAAQ6D,GACnBa,EAAII,IAAIG,gBAAgBJ,EAC5B,CAGQK,CAAuBtB,EAAGC,EAKlC"}