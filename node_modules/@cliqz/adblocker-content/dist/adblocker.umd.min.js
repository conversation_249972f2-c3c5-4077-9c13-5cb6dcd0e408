!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).adblocker={})}(this,(function(e){"use strict";const t="cliqz-adblocker-script",n=new Set(["br","head","link","meta","script","style","s"]);function s(e){return 1===e.nodeType}function o(e){const t=new Set(["br","head","link","meta","script","style","s"]),n=new Set,s=new Set,o=new Set,r=new Set;for(const i of e)for(const e of[i,...i.querySelectorAll("[id]:not(html):not(body),[class]:not(html):not(body),[href]:not(html):not(body)")]){if(r.has(e))continue;if(r.add(e),t.has(e.nodeName.toLowerCase()))continue;const i=e.getAttribute("id");"string"==typeof i&&o.add(i);const c=e.classList;for(const e of c)n.add(e);const a=e.getAttribute("href");"string"==typeof a&&s.add(a)}return{classes:Array.from(n),hrefs:Array.from(s),ids:Array.from(o)}}function r(e){return`try{${e}}catch(c){}!function(){var c=document.currentScript,e=c&&c.parentNode;e&&e.removeChild(c)}();`}function i(e,t){const n=t.head||t.documentElement||t;null!==n&&n.appendChild(e)}e.DOMMonitor=class{constructor(e){this.cb=e,this.knownIds=new Set,this.knownHrefs=new Set,this.knownClasses=new Set,this.observer=null}queryAll(e){this.cb({type:"elements",elements:[e.document.documentElement]}),this.handleUpdatedNodes([e.document.documentElement])}start(e){if(null===this.observer&&void 0!==e.MutationObserver){const n=new Set,o=()=>{this.handleUpdatedNodes(Array.from(n)),n.clear()},[r,i]=function(e,{waitFor:t,maxWait:n}){let s,o;const r=()=>{clearTimeout(s),clearTimeout(o),s=void 0,o=void 0},i=()=>{r(),e()};return[()=>{n>0&&void 0===o&&(o=setTimeout(i,n)),clearTimeout(s),s=setTimeout(i,t)},r]}(o,{waitFor:25,maxWait:1e3});this.observer=new e.MutationObserver((e=>{(function(e){const n=[];for(const o of e)if("attributes"===o.type)s(o.target)&&n.push(o.target);else if("childList"===o.type)for(const e of o.addedNodes)s(e)&&e.id!==t&&n.push(e);return n})(e).forEach(n.add,n),n.size>512?(i(),o()):r()})),this.observer.observe(e.document.documentElement,{attributes:!0,attributeFilter:["class","id","href"],childList:!0,subtree:!0})}}stop(){null!==this.observer&&(this.observer.disconnect(),this.observer=null)}handleNewFeatures({hrefs:e,ids:t,classes:n}){const s=[],o=[],r=[];for(const e of t)!1===this.knownIds.has(e)&&(s.push(e),this.knownIds.add(e));for(const e of n)!1===this.knownClasses.has(e)&&(o.push(e),this.knownClasses.add(e));for(const t of e)!1===this.knownHrefs.has(t)&&(r.push(t),this.knownHrefs.add(t));return(0!==s.length||0!==o.length||0!==r.length)&&(this.cb({type:"features",classes:o,hrefs:r,ids:s}),!0)}handleUpdatedNodes(e){return 0!==e.length&&(this.cb({type:"elements",elements:e.filter((e=>!1===n.has(e.nodeName.toLowerCase())))}),this.handleNewFeatures(o(e)))}},e.autoRemoveScript=r,e.extractFeaturesFromDOM=o,e.injectScript=function(e,n){!function(e){var t,n,s;try{return-1!==(null===(s=null===(n=null===(t=e.defaultView)||void 0===t?void 0:t.navigator)||void 0===n?void 0:n.userAgent)||void 0===s?void 0:s.indexOf("Firefox"))}catch(e){return!1}}(n)?function(e,n){const s=n.createElement("script");s.type="text/javascript",s.id=t,s.async=!1,s.appendChild(n.createTextNode(r(e))),i(s,n)}(e,n):async function(e,n){const s=n.defaultView,o=n.createElement("script");o.async=!1,o.id=t;const c=new s.Blob([r(e)],{type:"text/javascript; charset=utf-8"}),a=s.URL.createObjectURL(c);o.src=await a,i(o,n),s.URL.revokeObjectURL(a)}(e,n)}}));
//# sourceMappingURL=adblocker.umd.min.js.map
