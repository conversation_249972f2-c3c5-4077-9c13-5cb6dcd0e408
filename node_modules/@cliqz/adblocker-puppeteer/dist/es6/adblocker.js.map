{"version": 3, "file": "adblocker.js", "sourceRoot": "", "sources": ["../../adblocker.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAKH,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAE3C,OAAO,EAAE,aAAa,EAAE,OAAO,EAAe,MAAM,kBAAkB,CAAC;AAEvE,OAAO,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEhG,SAAS,KAAK,CAAC,YAAoB;IACjC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,KAA6B;IACnD,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,OAAO,KAAK,KAAK,IAAI,EAAE;QACrB,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACxB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,MAAM;SACP;QACD,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;KAC7B;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,OAA8B;IACjE,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IAClD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAC1B,MAAM,IAAI,GAAgB,OAAO,CAAC,YAAY,EAAE,CAAC;IAEjD,OAAO,OAAO,CAAC,cAAc,CAAC;QAC5B,uBAAuB,EAAE,OAAO;QAChC,SAAS,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,SAAS,EAAE;QACxC,SAAS;QACT,IAAI;QACJ,GAAG;KACJ,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,eAAe;IAK1B,YAA6B,IAAoB,EAAmB,OAAyB;QAAhE,SAAI,GAAJ,IAAI,CAAgB;QAAmB,YAAO,GAAP,OAAO,CAAkB;QAC3F,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,kBAAkB,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAChF,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE;YAC3C,+DAA+D;YAC/D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAC1C,yEAAyE;YACzE,MAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAC7C,SAAS;YACT,2DAA2D;YAC3D,6BAA6B;YAC7B,mEAAmE;YACnE,yEAAyE;YACzE,oEAAoE;YACpE,EAAE;YACF,oDAAoD;YACpD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SACzC;IACH,CAAC;IAEM,KAAK,CAAC,OAAO;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAC1C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;SAC/C;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE;YAC3C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5D;IACH,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,gBAAiB,SAAQ,aAAa;IAAnD;;QACmB,aAAQ,GAA6C,IAAI,OAAO,EAAE,CAAC;QACpF,6DAA6D;QACrD,aAAQ,GAAuB,SAAS,CAAC;QAgCjD,6EAA6E;QAC7E,uDAAuD;QACvD,6EAA6E;QAEtE,qBAAgB,GAAG,KAAK,EAAE,KAAsB,EAAE,EAAE;YACzD,IAAI;gBACF,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC3B;YAAC,OAAO,EAAE,EAAE;gBACX,SAAS;aACV;QACH,CAAC,CAAC;QAEM,YAAO,GAAG,KAAK,EAAE,KAAsB,EAAiB,EAAE;YAChE,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YAExB,IAAI,GAAG,KAAK,+BAA+B,EAAE;gBAC3C,OAAO;aACR;YAED,2EAA2E;YAC3E,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACzC,YAAY;YACd,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;YAEnC,4EAA4E;YAC5E,uEAAuE;YACvE,+CAA+C;YAC/C;gBACE,kDAAkD;gBAClD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBAC5E,MAAM;oBACN,QAAQ;oBACR,GAAG;oBAEH,uBAAuB;oBACvB,YAAY,EAAE,IAAI;oBAClB,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,oBAAoB,EAAE,IAAI;oBAE1B,wCAAwC;oBACxC,eAAe,EAAE,KAAK;iBACvB,CAAC,CAAC;gBAEH,IAAI,MAAM,KAAK,KAAK,EAAE;oBACpB,OAAO;iBACR;gBAED,OAAO,CAAC,GAAG,CAAC;oBACV,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,OAAO,CAAC;oBAC9C,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC;iBAC1C,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;oBACZ,YAAY;gBACd,CAAC,CAAC,CAAC;aACJ;YAED,sEAAsE;YACtE,4EAA4E;YAC5E,oEAAoE;YACpE,uEAAuE;YAEvE,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE;gBACzC,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;oBAC9B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,+BACjD,MAAM;wBACN,QAAQ;wBACR,GAAG,IAGA,MAAM;wBAET,wCAAwC;wBACxC,YAAY,EAAE,KAAK,EACnB,iBAAiB,EAAE,KAAK,EACxB,gBAAgB,EAAE,KAAK,EACvB,oBAAoB,EAAE,KAAK;wBAE3B,wCAAwC;wBACxC,eAAe,EAAE,IAAI,IACrB,CAAC;oBAEH,kCAAkC;oBAClC,IAAI,MAAM,KAAK,KAAK,EAAE;wBACpB,OAAO;qBACR;oBAED,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;wBACnD,YAAY;oBACd,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;YAEH,uEAAuE;YACvE,8EAA8E;YAC9E,EAAE;YACF,0CAA0C;YAC1C,wBAAwB;YACxB,gCAAgC;YAChC,uDAAuD;YACvD,0BAA0B;YAC1B,EAAE;YACF,wDAAwD;YACxD,sEAAsE;YACtE,mDAAmD;YACnD,IAAI,kBAAkB,GAAG,CAAC,CAAC;YAC3B,GAAG;gBACD,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE;oBACtB,MAAM;iBACP;gBAED,IAAI;oBACF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,iBAAiB,CACjD,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,sBAAsB,CAAC,CACpD,CAAC;oBACF,kBAAkB,IAAI,CAAC,CAAC;oBAExB,IAAI,kBAAkB,KAAK,EAAE,EAAE;wBAC7B,MAAM;qBACP;oBAED,IAAI,gBAAgB,KAAK,KAAK,EAAE;wBAC9B,MAAM;qBACP;iBACF;gBAAC,OAAO,EAAE,EAAE;oBACX,MAAM;iBACP;gBAED,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,KAAK,KAAK,EAAE;oBAChD,MAAM;iBACP;gBAED,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;aAClB,QAAQ,IAAI,EAAE;QACjB,CAAC,CAAC;QAEK,mCAA8B,GAAG,CAAC,eAAe,GAAG,CAAC,EAAE,EAAE,CAC9D,CAAC,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC;QAE7B,cAAS,GAAG,CAAC,OAA8B,EAAQ,EAAE;;YAC1D,IAAI,MAAA,OAAO,CAAC,4BAA4B,uDAAI,EAAE;gBAC5C,OAAO;aACR;YAED,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC5E,OAAO,CAAC,kBAAkB,EAAE,CAAC;aAC9B;YAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAE9B,IACE,OAAO,CAAC,WAAW,EAAE;gBACrB,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,EAC/E;gBACA,OAAO,CAAC,QAAQ,CAAC,MAAA,OAAO,CAAC,wBAAwB,uDAAI,EAAE,CAAC,CAAC,CAAC;gBAC1D,OAAO;aACR;YAED,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEhD,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC1B,IAAI,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;oBAC5C,OAAO,CAAC,OAAO,CACb;wBACE,MAAM,EAAE,GAAG;wBACX,OAAO,EAAE,EAAE;wBACX,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC;wBAC1C,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBAC/C,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;iBACH;qBAAM;oBACL,OAAO,CAAC,OAAO,CACb;wBACE,MAAM,EAAE,GAAG;wBACX,OAAO,EAAE,EAAE;wBACX,IAAI,EAAE,QAAQ,CAAC,IAAI;wBACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;qBAClC,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;iBACH;aACF;iBAAM,IAAI,KAAK,KAAK,IAAI,EAAE;gBACzB,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACjD;iBAAM;gBACL,OAAO,CAAC,QAAQ,CAAC,MAAA,OAAO,CAAC,wBAAwB,uDAAI,EAAE,CAAC,CAAC,CAAC;aAC3D;QACH,CAAC,CAAC;IAqEJ,CAAC;IArSC,6EAA6E;IAC7E,uDAAuD;IACvD,6EAA6E;IAEtE,KAAK,CAAC,oBAAoB,CAAC,IAAoB;QACpD,IAAI,OAAO,GAAgC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACnE,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,OAAO,OAAO,CAAC;SAChB;QAED,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjC,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC;QACvB,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,IAAoB;QACrD,MAAM,OAAO,GAAgC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrE,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACrE;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3B,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;IAEM,iBAAiB,CAAC,IAAoB;QAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAsMO,KAAK,CAAC,qBAAqB,CAAC,KAAsB,EAAE,MAAc;QACxE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,KAAK,CAAC,WAAW,CAAC;gBACtB,OAAO,EAAE,MAAM;aAChB,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CACrC,KAAsB,EACtB,OAAiB;QAEjB,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC1C,QAAQ,CAAC,IAAI,CACX,KAAK;qBACF,YAAY,CAAC;oBACZ,OAAO,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBACtC,CAAC;qBACD,IAAI,CAAC,GAAG,EAAE;oBACT,mBAAmB;gBACrB,CAAC,CAAC,CACL,CAAC;aACH;SACF;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,mBAAmB,CAAC,KAAsB;QACtD,MAAM,QAAQ,GAAoB,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QAExC,KAAK,MAAM,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC,QAAQ,EAAE,EAAE,CAC5E,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAO,EAAE,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,CAClD,EAAE;YACD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAC1B,OAAO,CAAC,cAAc,CAAC;gBACrB,GAAG;gBACH,SAAS;gBACT,IAAI,EAAE,WAAW;aAClB,CAAC,CACH,CAAC;YAEF,IAAI,KAAK,EAAE;gBACT,QAAQ,CAAC,IAAI,CACX,KAAK;qBACF,MAAM,CAAC,eAAe,GAAG,mBAAmB,GAAG,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;;oBAChE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;wBAC5B,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,WAAW,CAAC,MAAM,CAAC,CAAC;qBACzC;gBACH,CAAC,CAAC;qBACD,KAAK,CAAC,GAAG,EAAE;oBACV,YAAY;gBACd,CAAC,CAAC,CACL,CAAC;aACH;SACF;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;CACF;AAED,2DAA2D;AAC3D,cAAc,kBAAkB,CAAC"}